-- 暂停菜单状态 - 使用BaseState重构版本
local BaseState = require("src/states/BaseState")
local StateFactory = require("src/states/StateFactory")
local Settings = require("src/config/Settings")
local Button = require("src/ui/Button")
local Panel = require("src/ui/Panel")
local Text = require("src/ui/Text")
local StateManager = require("src/core/StateManager")

-- 创建暂停状态实例
local PauseState = StateFactory.createModalState({
    name = "pause",
    cursorType = "arrow",
    uiLayer = 100, -- 高层级，覆盖游戏
    showOverlay = true,
    overlayColor = {0, 0, 0, 0.7},
    escapeToClose = true,

    -- 状态特有数据
    selectedItem = 1,
    panel = nil,

    -- 自定义事件处理
    onEnter = function(self, data)
        self.selectedItem = 1

        -- 创建暂停面板
        self:createPausePanel()
    end,

    onExit = function(self)
        -- 清空状态数据
        self.panel = nil
    end,

    onKeypressed = function(self, key)
        if key == "up" then
            self:selectPreviousItem()
        elseif key == "down" then
            self:selectNextItem()
        elseif key == "return" or key == "space" then
            self:activateCurrentItem()
        end
    end,

    onMousemoved = function(self, x, y, dx, dy)
        -- 鼠标移动时更新按钮选择
        if self.panel and self.panel.buttons then
            for i, button in ipairs(self.panel.buttons) do
                if button:containsPoint(x, y) then
                    self:selectItem(i)
                    break
                end
            end
        end
    end
})

-- 创建暂停面板
function PauseState:createPausePanel()
    self.panel = Panel.createModal({
        x = 0,
        y = 0,
        width = 450,
        height = 350,
        title = Settings.getText("game_paused"),
        titleFont = "large"
    })

    -- 创建继续游戏按钮
    local continueBtn = Button.createPrimary({
        x = 0,
        y = 0,
        width = 300,
        height = 50,
        text = Settings.getText("continue_game"),
        onClick = function()
            StateManager.popState()
        end
    })

    -- 创建设置按钮
    local settingsBtn = Button.createSecondary({
        x = 0,
        y = 0,
        width = 300,
        height = 50,
        text = Settings.getText("game_settings"),
        onClick = function()
            StateManager.changeState("settings", {
                previousState = "pause"
            })
        end
    })

    -- 创建返回主菜单按钮
    local menuBtn = Button.createDanger({
        x = 0,
        y = 0,
        width = 300,
        height = 50,
        text = Settings.getText("return_to_menu"),
        onClick = function()
            StateManager.changeState("menu")
        end
    })

    -- 添加按钮到面板
    self.panel:addChild(continueBtn, 75, 0)
    self.panel:addChild(settingsBtn, 75, 60)
    self.panel:addChild(menuBtn, 75, 120)

    -- 存储按钮引用用于导航
    self.panel.buttons = {continueBtn, settingsBtn, menuBtn}

    -- 创建控制提示文本
    local controlText = Text.createHelp({
        x = 0,
        y = self.panel.y + self.panel.height + 20,
        width = love.graphics.getWidth(),
        height = 30,
        text = string.format("%s    %s    %s", Settings.getText("keyboard_mouse_select"),
            Settings.getText("enter_click_confirm"), Settings.getText("esc_continue")),
        align = Text.ALIGN.CENTER
    })

    -- 添加组件到UI管理器
    self:addComponent(self.panel)
    self:addComponent(controlText)

    -- 设置第一个按钮为选中状态
    self:selectItem(1)
end

-- 选择菜单项
function PauseState:selectItem(index)
    if not self.panel or not self.panel.buttons or not self.panel.buttons[index] then
        return
    end

    -- 取消之前的高亮
    if self.panel.buttons[self.selectedItem] then
        self.panel.buttons[self.selectedItem].hovered = false
    end

    -- 设置新的选择
    self.selectedItem = index
    self.panel.buttons[self.selectedItem].hovered = true
end

-- 选择上一项
function PauseState:selectPreviousItem()
    local newIndex = self.selectedItem - 1
    if newIndex < 1 then
        newIndex = #self.panel.buttons
    end
    self:selectItem(newIndex)
end

-- 选择下一项
function PauseState:selectNextItem()
    local newIndex = self.selectedItem + 1
    if newIndex > #self.panel.buttons then
        newIndex = 1
    end
    self:selectItem(newIndex)
end

-- 激活当前项
function PauseState:activateCurrentItem()
    if self.panel and self.panel.buttons and self.panel.buttons[self.selectedItem] then
        local button = self.panel.buttons[self.selectedItem]
        if button.onClick then
            button.onClick(button)
        end
    end
end

return PauseState
