-- 基础状态类 - 抽象所有状态的共同功能
local BaseState = {}
BaseState.__index = BaseState

-- 创建新的状态实例
function BaseState:new(config)
    local state = {
        -- 配置项
        uiLayer = config.uiLayer or 50,
        cursorType = config.cursorType or "arrow",
        backgroundColor = config.backgroundColor or {0.1, 0.1, 0.2, 1},

        -- 状态标识
        name = config.name or "Unknown",

        -- 组件管理
        components = {},

        -- 事件回调
        onEnter = config.onEnter,
        onExit = config.onExit,
        onUpdate = config.onUpdate,
        onDraw = config.onDraw,
        onKeypressed = config.onKeypressed,
        onMousepressed = config.onMousepressed,
        onMousemoved = config.onMousemoved,

        -- 状态标志
        initialized = false
    }

    setmetatable(state, self)
    return state
end

-- 进入状态
function BaseState:enter(data)
    print("=== 进入状态:", self.name, "===")

    -- 设置光标
    if CursorManager then
        CursorManager.setCursor(self.cursorType)
    end

    -- 清空UI层
    if UIManager then
        UIManager.clearLayer(self.uiLayer)
    end

    -- 清空组件列表
    self.components = {}

    -- 调用自定义进入逻辑
    if self.onEnter then
        self.onEnter(self, data)
    end

    self.initialized = true
end

-- 退出状态
function BaseState:exit()
    print("=== 退出状态:", self.name, "===")

    -- 清理UI组件
    if UIManager then
        UIManager.clearLayer(self.uiLayer)
    end

    -- 清空组件列表
    self.components = {}

    -- 调用自定义退出逻辑
    if self.onExit then
        self.onExit(self)
    end

    self.initialized = false
end

-- 更新状态
function BaseState:update(dt)
    if not self.initialized then
        return
    end

    -- 调用自定义更新逻辑
    if self.onUpdate then
        self.onUpdate(self, dt)
    end
end

-- 绘制状态
function BaseState:draw()
    if not self.initialized then
        return
    end

    -- 绘制默认背景
    self:drawBackground()

    -- 调用自定义绘制逻辑
    if self.onDraw then
        self.onDraw(self)
    end
end

-- 绘制背景
function BaseState:drawBackground()
    local width = love.graphics.getWidth()
    local height = love.graphics.getHeight()

    love.graphics.setColor(self.backgroundColor)
    love.graphics.rectangle("fill", 0, 0, width, height)
end

-- 键盘输入
function BaseState:keypressed(key)
    if not self.initialized then
        return
    end

    -- 全局快捷键处理
    if key == "escape" then
        self:handleEscapeKey()
        return
    end

    -- 调用自定义键盘处理逻辑
    if self.onKeypressed then
        self.onKeypressed(self, key)
    end
end

-- ESC键处理（可被子类重写）
function BaseState:handleEscapeKey()
    -- 默认行为：返回主菜单
    if StateManager and self.name ~= "menu" then
        StateManager.changeState("menu")
    end
end

-- 鼠标点击
function BaseState:mousepressed(x, y, button)
    if not self.initialized then
        return
    end

    -- 先让UI管理器处理
    local handled = false
    if UIManager then
        handled = UIManager.mousepressed(x, y, button)
    end

    -- 如果UI没有处理，调用自定义逻辑
    if not handled and self.onMousepressed then
        self.onMousepressed(self, x, y, button)
    end
end

-- 鼠标移动
function BaseState:mousemoved(x, y, dx, dy)
    if not self.initialized then
        return
    end

    -- 调用自定义鼠标移动逻辑
    if self.onMousemoved then
        self.onMousemoved(self, x, y, dx, dy)
    end
end

-- 辅助方法：添加UI组件
function BaseState:addComponent(component, layer)
    layer = layer or self.uiLayer
    if UIManager then
        UIManager.addComponent(component, layer)
        table.insert(self.components, {
            component = component,
            layer = layer
        })
    end
end

-- 辅助方法：创建简单背景（星空、粒子等）
function BaseState:createStarryBackground(starCount)
    starCount = starCount or 100
    local stars = {}

    for i = 1, starCount do
        table.insert(stars, {
            x = math.random(0, love.graphics.getWidth()),
            y = math.random(0, love.graphics.getHeight()),
            size = math.random(1, 3),
            brightness = math.random(0.3, 1.0),
            twinkleSpeed = math.random(0.5, 2.0)
        })
    end

    return stars
end

-- 辅助方法：更新星空背景
function BaseState:updateStarryBackground(stars, dt)
    for _, star in ipairs(stars) do
        star.brightness = star.brightness + (math.random(-0.5, 0.5) * star.twinkleSpeed * dt)
        star.brightness = math.max(0.3, math.min(1.0, star.brightness))
    end
end

-- 辅助方法：绘制星空背景
function BaseState:drawStarryBackground(stars)
    for _, star in ipairs(stars) do
        love.graphics.setColor(star.brightness, star.brightness, star.brightness, 1)
        love.graphics.circle("fill", star.x, star.y, star.size)
    end
end

-- 辅助方法：创建渐变背景
function BaseState:drawGradientBackground(topColor, bottomColor)
    topColor = topColor or {0.05, 0.05, 0.15, 1}
    bottomColor = bottomColor or {0.15, 0.1, 0.2, 1}

    local width = love.graphics.getWidth()
    local height = love.graphics.getHeight()

    for y = 0, height, 10 do
        local ratio = y / height
        local r = topColor[1] + ratio * (bottomColor[1] - topColor[1])
        local g = topColor[2] + ratio * (bottomColor[2] - topColor[2])
        local b = topColor[3] + ratio * (bottomColor[3] - topColor[3])
        local a = topColor[4] + ratio * (bottomColor[4] - topColor[4])

        love.graphics.setColor(r, g, b, a)
        love.graphics.rectangle("fill", 0, y, width, 10)
    end
end

return BaseState
