-- 状态工厂 - 快速创建常见类型的状态
local BaseState = require("src/states/BaseState")

local StateFactory = {}

-- 创建简单UI状态（如菜单、选择界面等）
function StateFactory.createUIState(config)
    config = config or {}

    -- 默认配置
    local defaultConfig = {
        name = config.name or "ui_state",
        cursorType = "arrow",
        uiLayer = 50,
        backgroundColor = {0.1, 0.1, 0.2, 1},

        -- 创建星空背景
        createStarryBg = config.createStarryBg or false,
        starCount = config.starCount or 100,

        -- 创建渐变背景
        useGradient = config.useGradient or false,
        topColor = config.topColor,
        bottomColor = config.bottomColor
    }

    -- 合并配置
    for key, value in pairs(config) do
        defaultConfig[key] = value
    end

    -- 扩展绘制逻辑
    local originalOnDraw = defaultConfig.onDraw
    defaultConfig.onDraw = function(self)
        -- 绘制背景效果
        if self.createStarryBg and self.stars then
            self:drawStarryBackground(self.stars)
        end

        if self.useGradient then
            self:drawGradientBackground(self.topColor, self.bottomColor)
        end

        -- 调用原始绘制逻辑
        if originalOnDraw then
            originalOnDraw(self)
        end
    end

    -- 扩展进入逻辑
    local originalOnEnter = defaultConfig.onEnter
    defaultConfig.onEnter = function(self, data)
        -- 创建背景效果
        if self.createStarryBg then
            self.stars = self:createStarryBackground(self.starCount)
        end

        -- 调用原始进入逻辑
        if originalOnEnter then
            originalOnEnter(self, data)
        end
    end

    -- 扩展更新逻辑
    local originalOnUpdate = defaultConfig.onUpdate
    defaultConfig.onUpdate = function(self, dt)
        -- 更新背景效果
        if self.createStarryBg and self.stars then
            self:updateStarryBackground(self.stars, dt)
        end

        -- 调用原始更新逻辑
        if originalOnUpdate then
            originalOnUpdate(self, dt)
        end
    end

    return BaseState:new(defaultConfig)
end

-- 创建游戏状态（需要自定义绘制逻辑）
function StateFactory.createGameState(config)
    config = config or {}

    local defaultConfig = {
        name = config.name or "game_state",
        cursorType = "crosshair",
        uiLayer = 20,
        backgroundColor = {0, 0, 0, 0}, -- 透明背景，游戏自己绘制

        -- 游戏特有配置
        pauseKey = config.pauseKey or "escape",
        pauseState = config.pauseState or "pause"
    }

    -- 合并配置
    for key, value in pairs(config) do
        defaultConfig[key] = value
    end

    -- 扩展键盘处理
    local originalOnKeypressed = defaultConfig.onKeypressed
    defaultConfig.onKeypressed = function(self, key)
        if key == self.pauseKey then
            if StateManager then
                StateManager.pushState(self.pauseState)
            end
            return
        end

        -- 调用原始键盘处理
        if originalOnKeypressed then
            originalOnKeypressed(self, key)
        end
    end

    -- 重写背景绘制为空（游戏状态自己处理背景）
    defaultConfig.drawBackground = function(self)
        -- 游戏状态不绘制默认背景
    end

    return BaseState:new(defaultConfig)
end

-- 创建模态对话框状态（暂停、设置等）
function StateFactory.createModalState(config)
    config = config or {}

    local defaultConfig = {
        name = config.name or "modal_state",
        cursorType = "arrow",
        uiLayer = 100, -- 高层级，覆盖其他UI
        backgroundColor = {0, 0, 0, 0.7}, -- 半透明遮罩

        -- 模态框特有配置
        showOverlay = config.showOverlay ~= false,
        overlayColor = config.overlayColor or {0, 0, 0, 0.7},
        escapeToClose = config.escapeToClose ~= false,
        clickOutsideToClose = config.clickOutsideToClose or false
    }

    -- 合并配置
    for key, value in pairs(config) do
        defaultConfig[key] = value
    end

    -- 自定义ESC处理
    if defaultConfig.escapeToClose then
        defaultConfig.handleEscapeKey = function(self)
            if StateManager then
                StateManager.popState()
            end
        end
    end

    -- 扩展绘制逻辑
    local originalOnDraw = defaultConfig.onDraw
    defaultConfig.onDraw = function(self)
        -- 绘制遮罩层
        if self.showOverlay then
            local width = love.graphics.getWidth()
            local height = love.graphics.getHeight()
            love.graphics.setColor(self.overlayColor)
            love.graphics.rectangle("fill", 0, 0, width, height)
        end

        -- 调用原始绘制逻辑
        if originalOnDraw then
            originalOnDraw(self)
        end
    end

    return BaseState:new(defaultConfig)
end

-- 快速创建简单菜单状态
function StateFactory.createMenuState(name, menuItems, options)
    options = options or {}

    return StateFactory.createUIState({
        name = name,
        createStarryBg = options.createStarryBg or true,
        starCount = options.starCount or 150,

        onEnter = function(self, data)
            -- 创建菜单标题
            if options.title then
                self:createTitle(options.title)
            end

            -- 创建菜单按钮
            self:createMenuButtons(menuItems, options)

            -- 创建控制说明
            if options.showControls then
                self:createControlText()
            end
        end,

        onKeypressed = function(self, key)
            -- 菜单导航逻辑
            self:handleMenuNavigation(key)
        end
    })
end

return StateFactory
