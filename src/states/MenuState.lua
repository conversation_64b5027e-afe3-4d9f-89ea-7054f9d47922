-- 主菜单状态 - 使用BaseState重构版本
local BaseState = require("src/states/BaseState")
local StateFactory = require("src/states/StateFactory")
local Settings = require("src/config/Settings")
local GameData = require("src/core/GameData")
local Button = require("src/ui/Button")
local Text = require("src/ui/Text")
local LocaleManager = require("src/locales/LocaleManager")
local StateManager = require("src/core/StateManager")

-- 创建主菜单状态实例
local MenuState = StateFactory.createUIState({
    name = "menu",
    cursorType = "arrow",
    createStarryBg = true,
    starCount = 150,
    useGradient = false,

    -- 状态特有数据
    selectedItem = 1,
    menuButtons = {},
    particles = {},

    -- 自定义事件处理
    onEnter = function(self, data)
        self.selectedItem = 1
        self.menuButtons = {}

        -- 创建魔法粒子效果
        self:createMagicParticles()

        -- 创建UI组件
        self:createTextComponents()
        self:createMenuButtons()
    end,

    onExit = function(self)
        -- 清空状态数据
        self.menuButtons = {}
        self.particles = {}
    end,

    onUpdate = function(self, dt)
        -- 更新魔法粒子
        self:updateMagicParticles(dt)
    end,

    onDraw = function(self)
        -- 绘制魔法粒子
        self:drawMagicParticles()
    end,

    onKeypressed = function(self, key)
        if key == "up" then
            self:selectPreviousItem()
        elseif key == "down" then
            self:selectNextItem()
        elseif key == "return" or key == "space" then
            self:activateCurrentItem()
        elseif key == "l" then
            -- 快捷键切换语言
            Settings.toggleLanguage()
            Settings.save()
            -- 重新创建组件以更新文本
            self:recreateComponents()
        end
    end,

    onMousemoved = function(self, x, y, dx, dy)
        -- 鼠标移动时更新按钮选择
        if self.menuButtons then
            for i, button in ipairs(self.menuButtons) do
                if button:containsPoint(x, y) then
                    self:selectItem(i)
                    break
                end
            end
        end
    end
})

-- 创建魔法粒子效果
function MenuState:createMagicParticles()
    self.particles = {}
    for i = 1, 30 do
        table.insert(self.particles, {
            x = math.random(0, love.graphics.getWidth()),
            y = math.random(0, love.graphics.getHeight()),
            vx = math.random(-20, 20),
            vy = math.random(-20, 20),
            life = math.random(2, 5),
            maxLife = math.random(2, 5),
            color = {math.random(0.5, 1), math.random(0.5, 1), math.random(0.8, 1)}
        })
    end
end

-- 更新魔法粒子
function MenuState:updateMagicParticles(dt)
    for i = #self.particles, 1, -1 do
        local p = self.particles[i]
        p.x = p.x + p.vx * dt
        p.y = p.y + p.vy * dt
        p.life = p.life - dt

        -- 边界循环
        local screenWidth = love.graphics.getWidth()
        local screenHeight = love.graphics.getHeight()

        if p.x < 0 then
            p.x = screenWidth
        end
        if p.x > screenWidth then
            p.x = 0
        end
        if p.y < 0 then
            p.y = screenHeight
        end
        if p.y > screenHeight then
            p.y = 0
        end

        -- 重置已死粒子
        if p.life <= 0 then
            p.x = math.random(0, screenWidth)
            p.y = math.random(0, screenHeight)
            p.vx = math.random(-20, 20)
            p.vy = math.random(-20, 20)
            p.life = math.random(2, 5)
            p.maxLife = p.life
            p.color = {math.random(0.5, 1), math.random(0.5, 1), math.random(0.8, 1)}
        end
    end
end

-- 绘制魔法粒子
function MenuState:drawMagicParticles()
    for _, p in ipairs(self.particles) do
        local alpha = p.life / p.maxLife
        love.graphics.setColor(p.color[1], p.color[2], p.color[3], alpha)
        love.graphics.circle("fill", p.x, p.y, 3)

        -- 添加发光效果
        love.graphics.setColor(p.color[1], p.color[2], p.color[3], alpha * 0.3)
        love.graphics.circle("fill", p.x, p.y, 8)
    end
end

-- 创建文本组件
function MenuState:createTextComponents()
    local screenWidth = love.graphics.getWidth()
    local screenHeight = love.graphics.getHeight()

    -- 主标题
    local titleText = Text.createTitle({
        x = 0,
        y = screenHeight * 0.15,
        width = screenWidth,
        height = 60,
        text = LocaleManager.getText("game_title"),
        font = "huge",
        align = Text.ALIGN.CENTER,
        textColor = {0.9, 0.7, 1, 1} -- 魔法紫色
    })

    -- 副标题
    local subtitleText = Text.createSubtitle({
        x = 0,
        y = screenHeight * 0.25,
        width = screenWidth,
        height = 40,
        text = LocaleManager.getText("game_subtitle"),
        font = "large",
        align = Text.ALIGN.CENTER,
        textColor = {0.7, 0.8, 0.9, 1}
    })

    -- 游戏描述
    local descText = Text.createDescription({
        x = 0,
        y = screenHeight * 0.35,
        width = screenWidth,
        height = 30,
        text = LocaleManager.getText("game_description"),
        font = "medium",
        align = Text.ALIGN.CENTER,
        textColor = {0.6, 0.6, 0.7, 1},
        multiline = false
    })

    -- 控制说明
    local controlText = Text.createHelp({
        x = 20,
        y = screenHeight - 40,
        width = screenWidth - 40,
        height = 20,
        text = string.format("%s    %s    %s", Settings.getText("keyboard_mouse_select"),
            Settings.getText("enter_click_confirm"), Settings.getText("f11_fullscreen")),
        font = "small",
        align = Text.ALIGN.LEFT,
        textColor = {0.5, 0.5, 0.6, 1}
    })

    -- 语言显示
    local languageText = Text.createHelp({
        x = screenWidth - 150,
        y = 20,
        width = 130,
        height = 20,
        text = "Language: " .. Settings.getText(Settings.get("language")),
        font = "small",
        align = Text.ALIGN.LEFT,
        textColor = {0.4, 0.4, 0.5, 1}
    })

    -- 游戏统计（如果有）
    local gameData = GameData.getData()
    if gameData and gameData.persistent.totalKills > 0 then
        local statsText1 = Text.createHelp({
            x = 20,
            y = screenHeight - 80,
            width = 200,
            height = 20,
            text = LocaleManager.getText("total_kills") .. ": " .. gameData.persistent.totalKills,
            font = "small",
            align = Text.ALIGN.LEFT,
            textColor = {0.4, 0.4, 0.5, 1}
        })

        local statsText2 = Text.createHelp({
            x = 20,
            y = screenHeight - 60,
            width = 200,
            height = 20,
            text = LocaleManager.getText("highest_level") .. ": " .. gameData.persistent.highestLevel,
            font = "small",
            align = Text.ALIGN.LEFT,
            textColor = {0.4, 0.4, 0.5, 1}
        })

        self:addComponent(statsText1)
        self:addComponent(statsText2)
    end

    -- 添加文本组件
    self:addComponent(titleText)
    self:addComponent(subtitleText)
    self:addComponent(descText)
    self:addComponent(controlText)
    self:addComponent(languageText)
end

-- 创建菜单按钮
function MenuState:createMenuButtons()
    self.menuButtons = {}

    local screenWidth = love.graphics.getWidth()
    local screenHeight = love.graphics.getHeight()
    local buttonWidth = 300
    local buttonHeight = 50
    local buttonX = (screenWidth - buttonWidth) / 2
    local startY = screenHeight * 0.5
    local buttonSpacing = 60

    -- 开始冒险按钮
    local startBtn = Button.createPrimary({
        x = buttonX,
        y = startY,
        width = buttonWidth,
        height = buttonHeight,
        text = Settings.getText("start_adventure"),
        onClick = function()
            StateManager.changeState("character_select")
        end
    })

    -- 角色选择按钮
    local characterBtn = Button.createSecondary({
        x = buttonX,
        y = startY + buttonSpacing,
        width = buttonWidth,
        height = buttonHeight,
        text = Settings.getText("character_select"),
        onClick = function()
            StateManager.changeState("character_select")
        end
    })

    -- 游戏设置按钮
    local settingsBtn = Button.createSecondary({
        x = buttonX,
        y = startY + buttonSpacing * 2,
        width = buttonWidth,
        height = buttonHeight,
        text = Settings.getText("game_settings"),
        onClick = function()
            StateManager.changeState("settings", {
                previousState = "menu"
            })
        end
    })

    -- 退出游戏按钮
    local quitBtn = Button.createDanger({
        x = buttonX,
        y = startY + buttonSpacing * 3,
        width = buttonWidth,
        height = buttonHeight,
        text = Settings.getText("exit_game"),
        onClick = function()
            love.event.quit()
        end
    })

    -- 存储按钮引用
    self.menuButtons = {startBtn, characterBtn, settingsBtn, quitBtn}

    -- 添加按钮到UI管理器
    for _, button in ipairs(self.menuButtons) do
        self:addComponent(button)
    end

    -- 设置第一个按钮为选中状态
    self:selectItem(1)
end

-- 选择菜单项
function MenuState:selectItem(index)
    if not self.menuButtons or not self.menuButtons[index] then
        return
    end

    -- 取消之前的高亮
    if self.menuButtons[self.selectedItem] then
        self.menuButtons[self.selectedItem].hovered = false
    end

    -- 设置新的选择
    self.selectedItem = index
    self.menuButtons[self.selectedItem].hovered = true
end

-- 选择上一项
function MenuState:selectPreviousItem()
    local newIndex = self.selectedItem - 1
    if newIndex < 1 then
        newIndex = #self.menuButtons
    end
    self:selectItem(newIndex)
end

-- 选择下一项
function MenuState:selectNextItem()
    local newIndex = self.selectedItem + 1
    if newIndex > #self.menuButtons then
        newIndex = 1
    end
    self:selectItem(newIndex)
end

-- 激活当前项
function MenuState:activateCurrentItem()
    if self.menuButtons[self.selectedItem] then
        local button = self.menuButtons[self.selectedItem]
        if button.onClick then
            button.onClick(button)
        end
    end
end

-- 重新创建组件（用于语言切换）
function MenuState:recreateComponents()
    -- 清空当前组件
    if UIManager then
        UIManager.clearLayer(self.uiLayer)
    end
    self.components = {}

    -- 重新创建
    self:createTextComponents()
    self:createMenuButtons()
end

return MenuState
