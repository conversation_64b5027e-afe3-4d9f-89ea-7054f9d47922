-- 游戏主状态 - 使用BaseState重构版本
local BaseState = require("src/states/BaseState")
local StateFactory = require("src/states/StateFactory")
local Settings = require("src/config/Settings")
local GameData = require("src/core/GameData")
local GameSession = require("src/systems/GameSession")
local Text = require("src/ui/Text")
local StateManager = require("src/core/StateManager")

-- 游戏对象和状态
local gameUI = {}
local gameRunning = false
local gameTime = 0
local waveLevel = 1
local enemySpawnTimer = 0
local enemySpawnRate = 2.0

-- 游戏实体
local player = nil
local enemies = {}
local projectiles = {}
local pickups = {}

-- 摄像机
local camera = {
    x = 0,
    y = 0,
    zoom = 1.0
}

-- 游戏世界设置
local WORLD_WIDTH = 2000
local WORLD_HEIGHT = 2000

-- 创建游戏状态实例
local GameState = StateFactory.createGameState({
    name = "game",
    cursorType = "crosshair",
    uiLayer = 20,
    pauseKey = "escape",
    pauseState = "pause",

    -- 自定义事件处理
    onEnter = function(self, data)
        print("进入游戏状态...")

        -- 检查是否是新游戏
        local isNewGame = (data and data.newGame) or (player == nil)

        if isNewGame then
            -- 重置游戏数据
            GameData.resetCurrentRun()

            -- 初始化游戏会话
            GameSession.initialize()

            -- 创建玩家
            player = self:createPlayer()
            GameSession.setPlayer(player)

            -- 重置游戏状态
            enemies = {}
            projectiles = {}
            pickups = {}
            gameTime = 0
            waveLevel = 1
            enemySpawnTimer = 0
            enemySpawnRate = 2.0
            gameRunning = true

            print("新游戏初始化完成")
        else
            print("从暂停状态恢复游戏")
        end

        -- 创建游戏UI（总是重新创建）
        self:createGameUI()

        -- 游戏状态设为运行
        gameRunning = true
    end,

    onExit = function(self)
        -- 清理游戏会话
        GameSession.cleanup()

        -- 清理UI
        gameUI = {}

        print("退出游戏状态")
    end,

    onUpdate = function(self, dt)
        if not gameRunning then
            return
        end

        -- 更新游戏时间
        gameTime = gameTime + dt
        GameSession.updateTime(dt)

        -- 更新游戏对象
        self:updatePlayer(dt)
        self:spawnEnemies(dt)
        self:updateEnemies(dt)
        self:updateProjectiles(dt)
        self:updatePickups(dt)
        self:updateCamera(dt)
        self:updateGameUI(dt)
    end,

    onDraw = function(self)
        if not gameRunning then
            return
        end

        -- 绘制游戏世界
        self:drawWorld()
    end,

    onKeypressed = function(self, key)
        -- ESC键由StateFactory的game state自动处理暂停
        -- 其他游戏控制
        if key == "space" then
            self:playerShoot()
        end
    end,

    onMousepressed = function(self, x, y, button)
        if button == 1 then -- 左键射击
            self:playerShoot()
        end
    end
})

-- 创建玩家
function GameState:createPlayer()
    local gameData = GameData.getData()
    local characterData = GameData.getSelectedCharacter()

    return {
        x = WORLD_WIDTH / 2,
        y = WORLD_HEIGHT / 2,
        width = 32,
        height = 32,
        speed = characterData.speed or 150,
        health = characterData.maxHealth or 100,
        maxHealth = characterData.maxHealth or 100,
        level = 1,
        experience = 0,
        color = characterData.color or {0.2, 0.8, 0.2, 1},

        -- 武器属性
        damage = characterData.damage or 25,
        attackRange = characterData.attackRange or 200,
        attackCooldown = characterData.attackCooldown or 0.5,
        lastAttack = 0
    }
end

-- 创建游戏UI
function GameState:createGameUI()
    gameUI = {}

    -- 健康条
    local healthBar = Text.createDescription({
        x = 20,
        y = 20,
        width = 200,
        height = 30,
        text = "",
        align = Text.ALIGN.LEFT,
        textColor = {1, 0.2, 0.2, 1}
    })

    -- 等级显示
    local levelText = Text.createDescription({
        x = 20,
        y = 50,
        width = 200,
        height = 30,
        text = "",
        align = Text.ALIGN.LEFT,
        textColor = {0.8, 0.8, 0.2, 1}
    })

    -- 经验条
    local expBar = Text.createDescription({
        x = 20,
        y = 80,
        width = 200,
        height = 30,
        text = "",
        align = Text.ALIGN.LEFT,
        textColor = {0.2, 0.2, 1, 1}
    })

    -- 时间显示
    local timeText = Text.createDescription({
        x = love.graphics.getWidth() - 200,
        y = 20,
        width = 180,
        height = 30,
        text = "",
        align = Text.ALIGN.RIGHT,
        textColor = {0.8, 0.8, 0.8, 1}
    })

    gameUI.healthBar = healthBar
    gameUI.levelText = levelText
    gameUI.expBar = expBar
    gameUI.timeText = timeText

    -- 添加到UI管理器
    self:addComponent(healthBar)
    self:addComponent(levelText)
    self:addComponent(expBar)
    self:addComponent(timeText)
end

-- 更新游戏UI
function GameState:updateGameUI(dt)
    if not player or not gameUI then
        return
    end

    -- 更新健康条
    local healthPercent = (player.health / player.maxHealth) * 100
    gameUI.healthBar:setText(string.format("Health: %d/%d (%.1f%%)", math.floor(player.health), player.maxHealth,
        healthPercent))

    -- 更新等级
    gameUI.levelText:setText(string.format("Level: %d", player.level))

    -- 更新经验条
    local expNeeded = player.level * 100
    local expPercent = (player.experience / expNeeded) * 100
    gameUI.expBar:setText(string.format("EXP: %d/%d (%.1f%%)", player.experience, expNeeded, expPercent))

    -- 更新时间
    local minutes = math.floor(gameTime / 60)
    local seconds = math.floor(gameTime % 60)
    gameUI.timeText:setText(string.format("Time: %02d:%02d", minutes, seconds))
end

-- 更新玩家
function GameState:updatePlayer(dt)
    if not player then
        return
    end

    -- 玩家移动
    local dx, dy = 0, 0
    if love.keyboard.isDown("a", "left") then
        dx = dx - 1
    end
    if love.keyboard.isDown("d", "right") then
        dx = dx + 1
    end
    if love.keyboard.isDown("w", "up") then
        dy = dy - 1
    end
    if love.keyboard.isDown("s", "down") then
        dy = dy + 1
    end

    -- 标准化移动向量
    if dx ~= 0 or dy ~= 0 then
        local length = math.sqrt(dx * dx + dy * dy)
        dx = dx / length
        dy = dy / length
    end

    -- 应用移动
    player.x = player.x + dx * player.speed * dt
    player.y = player.y + dy * player.speed * dt

    -- 限制在世界边界内
    player.x = math.max(player.width / 2, math.min(WORLD_WIDTH - player.width / 2, player.x))
    player.y = math.max(player.height / 2, math.min(WORLD_HEIGHT - player.height / 2, player.y))

    -- 更新攻击冷却
    player.lastAttack = math.max(0, player.lastAttack - dt)

    -- 自动攻击最近的敌人
    self:autoAttack(dt)
end

-- 自动攻击
function GameState:autoAttack(dt)
    if not player or player.lastAttack > 0 then
        return
    end

    -- 寻找最近的敌人
    local nearestEnemy = nil
    local nearestDistance = player.attackRange

    for _, enemy in ipairs(enemies) do
        local dx = enemy.x - player.x
        local dy = enemy.y - player.y
        local distance = math.sqrt(dx * dx + dy * dy)

        if distance < nearestDistance then
            nearestEnemy = enemy
            nearestDistance = distance
        end
    end

    -- 攻击最近的敌人
    if nearestEnemy then
        self:shootAtTarget(nearestEnemy)
        player.lastAttack = player.attackCooldown
    end
end

-- 射击目标
function GameState:shootAtTarget(target)
    if not player or not target then
        return
    end

    local dx = target.x - player.x
    local dy = target.y - player.y
    local distance = math.sqrt(dx * dx + dy * dy)

    if distance > 0 then
        -- 创建子弹
        local projectile = {
            x = player.x,
            y = player.y,
            vx = (dx / distance) * 400, -- 子弹速度
            vy = (dy / distance) * 400,
            damage = player.damage,
            life = 3.0, -- 3秒生存时间
            width = 8,
            height = 8,
            color = {1, 1, 0.2, 1}
        }

        table.insert(projectiles, projectile)
    end
end

-- 玩家射击
function GameState:playerShoot()
    if not player or player.lastAttack > 0 then
        return
    end

    -- 获取鼠标位置
    local mouseX, mouseY = love.mouse.getPosition()

    -- 转换为世界坐标
    local worldMouseX = mouseX + camera.x - love.graphics.getWidth() / 2
    local worldMouseY = mouseY + camera.y - love.graphics.getHeight() / 2

    -- 创建目标位置
    local target = {
        x = worldMouseX,
        y = worldMouseY
    }
    self:shootAtTarget(target)

    player.lastAttack = player.attackCooldown
end

-- 生成敌人
function GameState:spawnEnemies(dt)
    enemySpawnTimer = enemySpawnTimer + dt

    if enemySpawnTimer >= enemySpawnRate then
        enemySpawnTimer = 0

        -- 在屏幕边缘生成敌人
        local spawnX, spawnY = self:getSpawnPosition()

        local enemy = {
            x = spawnX,
            y = spawnY,
            width = 24,
            height = 24,
            speed = 50 + waveLevel * 5,
            health = 50 + waveLevel * 10,
            maxHealth = 50 + waveLevel * 10,
            damage = 10 + waveLevel * 2,
            color = {1, 0.2, 0.2, 1}
        }

        table.insert(enemies, enemy)

        -- 增加难度
        enemySpawnRate = math.max(0.5, enemySpawnRate - 0.01)
        if #enemies % 20 == 0 then
            waveLevel = waveLevel + 1
        end
    end
end

-- 获取生成位置
function GameState:getSpawnPosition()
    local side = math.random(1, 4)
    local x, y

    if side == 1 then -- 左
        x = camera.x - love.graphics.getWidth() / 2 - 50
        y = camera.y + math.random(-love.graphics.getHeight() / 2, love.graphics.getHeight() / 2)
    elseif side == 2 then -- 右
        x = camera.x + love.graphics.getWidth() / 2 + 50
        y = camera.y + math.random(-love.graphics.getHeight() / 2, love.graphics.getHeight() / 2)
    elseif side == 3 then -- 上
        x = camera.x + math.random(-love.graphics.getWidth() / 2, love.graphics.getWidth() / 2)
        y = camera.y - love.graphics.getHeight() / 2 - 50
    else -- 下
        x = camera.x + math.random(-love.graphics.getWidth() / 2, love.graphics.getWidth() / 2)
        y = camera.y + love.graphics.getHeight() / 2 + 50
    end

    -- 确保在世界边界内
    x = math.max(50, math.min(WORLD_WIDTH - 50, x))
    y = math.max(50, math.min(WORLD_HEIGHT - 50, y))

    return x, y
end

-- 更新敌人
function GameState:updateEnemies(dt)
    for i = #enemies, 1, -1 do
        local enemy = enemies[i]

        -- 向玩家移动
        if player then
            local dx = player.x - enemy.x
            local dy = player.y - enemy.y
            local distance = math.sqrt(dx * dx + dy * dy)

            if distance > 0 then
                enemy.x = enemy.x + (dx / distance) * enemy.speed * dt
                enemy.y = enemy.y + (dy / distance) * enemy.speed * dt
            end

            -- 检查与玩家碰撞
            if self:checkCollision(enemy, player) then
                player.health = player.health - enemy.damage * dt

                if player.health <= 0 then
                    -- 游戏结束
                    gameRunning = false
                    StateManager.changeState("gameover", {
                        survivalTime = gameTime,
                        enemiesKilled = waveLevel * 20 - #enemies,
                        level = player.level,
                        experience = player.experience
                    })
                    return
                end
            end
        end
    end
end

-- 更新弹射体
function GameState:updateProjectiles(dt)
    for i = #projectiles, 1, -1 do
        local proj = projectiles[i]

        -- 移动弹射体
        proj.x = proj.x + proj.vx * dt
        proj.y = proj.y + proj.vy * dt
        proj.life = proj.life - dt

        -- 移除超时弹射体
        if proj.life <= 0 then
            table.remove(projectiles, i)
        else
            -- 检查与敌人碰撞
            for j = #enemies, 1, -1 do
                local enemy = enemies[j]
                if self:checkCollision(proj, enemy) then
                    enemy.health = enemy.health - proj.damage
                    table.remove(projectiles, i)

                    if enemy.health <= 0 then
                        table.remove(enemies, j)
                        player.experience = player.experience + 10

                        -- 升级检查
                        local expNeeded = player.level * 100
                        if player.experience >= expNeeded then
                            player.level = player.level + 1
                            player.experience = player.experience - expNeeded
                            player.maxHealth = player.maxHealth + 20
                            player.health = math.min(player.health + 20, player.maxHealth)

                            -- 跳转到升级界面
                            print("玩家升级到等级", player.level, "- 进入升级界面")
                            StateManager.changeState("upgrade")
                            return
                        end
                    end
                    break
                end
            end
        end
    end
end

-- 更新道具
function GameState:updatePickups(dt)
    -- TODO: 实现道具系统
end

-- 更新摄像机
function GameState:updateCamera(dt)
    if not player then
        return
    end

    -- 摄像机跟随玩家，不受地图边界限制
    camera.x = player.x + player.width / 2 - love.graphics.getWidth() / 2
    camera.y = player.y + player.height / 2 - love.graphics.getHeight() / 2
end

-- 碰撞检测
function GameState:checkCollision(a, b)
    return a.x < b.x + b.width and a.x + a.width > b.x and a.y < b.y + b.height and a.y + a.height > b.y
end

-- 绘制游戏世界
function GameState:drawWorld()
    -- 应用摄像机变换
    love.graphics.push()
    love.graphics.translate(-camera.x, -camera.y)

    -- 绘制世界背景
    self:drawBackground()

    -- 绘制游戏对象
    self:drawPlayer()
    self:drawEnemies()
    self:drawProjectiles()
    self:drawPickups()

    love.graphics.pop()
end

-- 绘制背景
function GameState:drawBackground()
    -- 绘制网格背景
    love.graphics.setColor(0.1, 0.1, 0.15, 1)
    love.graphics.rectangle("fill", 0, 0, WORLD_WIDTH, WORLD_HEIGHT)

    -- 绘制网格线
    love.graphics.setColor(0.2, 0.2, 0.25, 1)
    love.graphics.setLineWidth(1)

    local gridSize = 50
    for x = 0, WORLD_WIDTH, gridSize do
        love.graphics.line(x, 0, x, WORLD_HEIGHT)
    end
    for y = 0, WORLD_HEIGHT, gridSize do
        love.graphics.line(0, y, WORLD_WIDTH, y)
    end

    -- 绘制世界边界
    love.graphics.setColor(0.5, 0.3, 0.3, 1)
    love.graphics.setLineWidth(3)
    love.graphics.rectangle("line", 0, 0, WORLD_WIDTH, WORLD_HEIGHT)
end

-- 绘制玩家
function GameState:drawPlayer()
    if not player then
        return
    end

    love.graphics.setColor(player.color)
    love.graphics.rectangle("fill", player.x - player.width / 2, player.y - player.height / 2, player.width,
        player.height)

    -- 绘制健康条
    local barWidth = player.width
    local barHeight = 4
    local healthPercent = player.health / player.maxHealth

    love.graphics.setColor(0.3, 0.3, 0.3, 1)
    love.graphics.rectangle("fill", player.x - barWidth / 2, player.y - player.height / 2 - 10, barWidth, barHeight)

    love.graphics.setColor(1 - healthPercent, healthPercent, 0, 1)
    love.graphics.rectangle("fill", player.x - barWidth / 2, player.y - player.height / 2 - 10,
        barWidth * healthPercent, barHeight)
end

-- 绘制敌人
function GameState:drawEnemies()
    for _, enemy in ipairs(enemies) do
        love.graphics.setColor(enemy.color)
        love.graphics
            .rectangle("fill", enemy.x - enemy.width / 2, enemy.y - enemy.height / 2, enemy.width, enemy.height)

        -- 绘制健康条
        local barWidth = enemy.width
        local barHeight = 3
        local healthPercent = enemy.health / enemy.maxHealth

        love.graphics.setColor(0.3, 0.3, 0.3, 1)
        love.graphics.rectangle("fill", enemy.x - barWidth / 2, enemy.y - enemy.height / 2 - 8, barWidth, barHeight)

        love.graphics.setColor(1 - healthPercent, healthPercent, 0, 1)
        love.graphics.rectangle("fill", enemy.x - barWidth / 2, enemy.y - enemy.height / 2 - 8,
            barWidth * healthPercent, barHeight)
    end
end

-- 绘制弹射体
function GameState:drawProjectiles()
    for _, proj in ipairs(projectiles) do
        love.graphics.setColor(proj.color)
        love.graphics.rectangle("fill", proj.x - proj.width / 2, proj.y - proj.height / 2, proj.width, proj.height)
    end
end

-- 绘制道具
function GameState:drawPickups()
    -- TODO: 实现道具绘制
end

return GameState
