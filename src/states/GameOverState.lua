-- 游戏结束状态 - 使用BaseState重构版本
local BaseState = require("src/states/BaseState")
local StateFactory = require("src/states/StateFactory")
local Settings = require("src/config/Settings")
local Button = require("src/ui/Button")
local Panel = require("src/ui/Panel")
local Text = require("src/ui/Text")
local StateManager = require("src/core/StateManager")

-- 创建游戏结束状态实例
local GameOverState = StateFactory.createModalState({
    name = "gameover",
    cursorType = "arrow",
    uiLayer = 100,
    showOverlay = true,
    overlayColor = {0, 0, 0, 0.8},
    escapeToClose = false, -- 游戏结束不允许ESC直接退出

    -- 状态特有数据
    selectedItem = 1,
    gameStats = nil,
    panel = nil,

    -- 自定义事件处理
    onEnter = function(self, data)
        self.selectedItem = 1

        -- 获取游戏数据
        self.gameStats = data or {}

        -- 创建游戏结束面板
        self:createGameOverPanel()
    end,

    onExit = function(self)
        -- 清空状态数据
        self.gameStats = nil
        self.panel = nil
    end,

    onKeypressed = function(self, key)
        if key == "up" then
            self:selectPreviousItem()
        elseif key == "down" then
            self:selectNextItem()
        elseif key == "return" or key == "space" then
            self:activateCurrentItem()
        elseif key == "escape" then
            -- ESC键直接返回主菜单
            StateManager.changeState("menu")
        end
    end,

    onMousemoved = function(self, x, y, dx, dy)
        -- 鼠标移动时更新按钮选择
        if self.panel and self.panel.buttons then
            for i, button in ipairs(self.panel.buttons) do
                if button:containsPoint(x, y) then
                    self:selectItem(i)
                    break
                end
            end
        end
    end
})

-- 创建游戏结束面板
function GameOverState:createGameOverPanel()
    self.panel = Panel.createModal({
        x = 0,
        y = 0,
        width = 500,
        height = 400,
        title = Settings.getText("game_over") or "游戏结束",
        titleFont = "large",
        backgroundColor = {0.15, 0.15, 0.25, 0.95}
    })

    -- 创建统计信息显示
    self:createStatsDisplay()

    -- 创建操作按钮
    self:createActionButtons()

    -- 创建控制提示
    local controlText = Text.createHelp({
        x = 0,
        y = self.panel.y + self.panel.height + 20,
        width = love.graphics.getWidth(),
        height = 30,
        text = string.format("%s    %s    %s", Settings.getText("keyboard_mouse_select") or "↑↓/鼠标 选择",
            Settings.getText("enter_click_confirm") or "回车/点击 确认",
            Settings.getText("esc_menu") or "ESC 主菜单"),
        align = Text.ALIGN.CENTER
    })

    -- 添加组件到UI管理器
    self:addComponent(self.panel)
    self:addComponent(controlText)

    -- 设置第一个按钮为选中状态
    self:selectItem(1)
end

-- 创建统计信息显示
function GameOverState:createStatsDisplay()
    local yOffset = 20
    local lineHeight = 25

    if not self.gameStats then
        return
    end

    -- 存活时间
    if self.gameStats.survivalTime then
        local timeText = Text.createDescription({
            x = 20,
            y = yOffset,
            width = self.panel.width - 40,
            height = lineHeight,
            text = (Settings.getText("survival_time") or "存活时间") .. ": " ..
                string.format("%.1f", self.gameStats.survivalTime) .. (Settings.getText("seconds") or "秒"),
            align = Text.ALIGN.LEFT,
            textColor = {0.9, 0.9, 0.9, 1}
        })
        self.panel:addChild(timeText, 20, yOffset)
        yOffset = yOffset + lineHeight
    end

    -- 击杀数量
    if self.gameStats.enemiesKilled then
        local killText = Text.createDescription({
            x = 20,
            y = yOffset,
            width = self.panel.width - 40,
            height = lineHeight,
            text = (Settings.getText("enemies_killed") or "击杀敌人") .. ": " .. self.gameStats.enemiesKilled,
            align = Text.ALIGN.LEFT,
            textColor = {0.9, 0.9, 0.9, 1}
        })
        self.panel:addChild(killText, 20, yOffset)
        yOffset = yOffset + lineHeight
    end

    -- 等级
    if self.gameStats.level then
        local levelText = Text.createDescription({
            x = 20,
            y = yOffset,
            width = self.panel.width - 40,
            height = lineHeight,
            text = (Settings.getText("reached_level") or "达到等级") .. ": " .. self.gameStats.level,
            align = Text.ALIGN.LEFT,
            textColor = {0.9, 0.9, 0.9, 1}
        })
        self.panel:addChild(levelText, 20, yOffset)
        yOffset = yOffset + lineHeight
    end

    -- 收集的经验值
    if self.gameStats.experience then
        local expText = Text.createDescription({
            x = 20,
            y = yOffset,
            width = self.panel.width - 40,
            height = lineHeight,
            text = (Settings.getText("experience_gained") or "获得经验") .. ": " .. self.gameStats.experience,
            align = Text.ALIGN.LEFT,
            textColor = {0.9, 0.9, 0.9, 1}
        })
        self.panel:addChild(expText, 20, yOffset)
        yOffset = yOffset + lineHeight
    end

    -- 最佳表现提示
    if self.gameStats.isNewRecord then
        local recordText = Text.createTitle({
            x = 20,
            y = yOffset + 10,
            width = self.panel.width - 40,
            height = lineHeight,
            text = Settings.getText("new_record") or "新纪录！",
            align = Text.ALIGN.CENTER,
            textColor = {1, 0.8, 0.2, 1}
        })
        self.panel:addChild(recordText, 20, yOffset + 10)
    end

    return yOffset + 20
end

-- 创建操作按钮
function GameOverState:createActionButtons()
    local buttonY = 220
    local buttonSpacing = 60

    -- 重新开始按钮
    local restartBtn = Button.createPrimary({
        x = 50,
        y = buttonY,
        width = 300,
        height = 50,
        text = Settings.getText("restart_game") or "重新开始",
        onClick = function()
            StateManager.changeState("game", {
                newGame = true
            })
        end
    })

    -- 角色选择按钮
    local characterBtn = Button.createSecondary({
        x = 50,
        y = buttonY + buttonSpacing,
        width = 300,
        height = 50,
        text = Settings.getText("character_select") or "角色选择",
        onClick = function()
            StateManager.changeState("character_select")
        end
    })

    -- 返回主菜单按钮
    local menuBtn = Button.createSecondary({
        x = 50,
        y = buttonY + buttonSpacing * 2,
        width = 300,
        height = 50,
        text = Settings.getText("return_to_menu") or "返回主菜单",
        onClick = function()
            StateManager.changeState("menu")
        end
    })

    -- 添加按钮到面板
    self.panel:addChild(restartBtn, 100, buttonY - self.panel.y)
    self.panel:addChild(characterBtn, 100, buttonY + buttonSpacing - self.panel.y)
    self.panel:addChild(menuBtn, 100, buttonY + buttonSpacing * 2 - self.panel.y)

    -- 存储按钮引用用于导航
    self.panel.buttons = {restartBtn, characterBtn, menuBtn}
end

-- 选择菜单项
function GameOverState:selectItem(index)
    if not self.panel or not self.panel.buttons or not self.panel.buttons[index] then
        return
    end

    -- 取消之前的高亮
    if self.panel.buttons[self.selectedItem] then
        self.panel.buttons[self.selectedItem].hovered = false
    end

    -- 设置新的选择
    self.selectedItem = index
    self.panel.buttons[self.selectedItem].hovered = true
end

-- 选择上一项
function GameOverState:selectPreviousItem()
    local newIndex = self.selectedItem - 1
    if newIndex < 1 then
        newIndex = #self.panel.buttons
    end
    self:selectItem(newIndex)
end

-- 选择下一项
function GameOverState:selectNextItem()
    local newIndex = self.selectedItem + 1
    if newIndex > #self.panel.buttons then
        newIndex = 1
    end
    self:selectItem(newIndex)
end

-- 激活当前项
function GameOverState:activateCurrentItem()
    if self.panel and self.panel.buttons and self.panel.buttons[self.selectedItem] then
        local button = self.panel.buttons[self.selectedItem]
        if button.onClick then
            button.onClick(button)
        end
    end
end

function GameOverState.exit()
    -- 清理UI组件
    UIManager.clearLayer(100)
    panel = nil
    gameStats = nil
end

function GameOverState.update(dt)
    -- 游戏结束状态不需要更新逻辑
    -- UI组件的更新由UIManager处理
end

function GameOverState.draw()
    -- 绘制半透明黑色背景
    love.graphics.setColor(0, 0, 0, 0.8)
    love.graphics.rectangle("fill", 0, 0, love.graphics.getWidth(), love.graphics.getHeight())

    -- 添加一些装饰性的红色粒子效果（表示失败/死亡）
    love.graphics.setColor(0.8, 0.2, 0.2, 0.3)
    local time = love.timer.getTime()
    for i = 1, 20 do
        local x = (math.sin(time * 0.5 + i) * 100 + love.graphics.getWidth() * 0.5)
        local y = (math.cos(time * 0.3 + i * 2) * 50 + love.graphics.getHeight() * 0.3)
        love.graphics.circle("fill", x, y, 4 + math.sin(time * 2 + i) * 2)
    end

    -- UI组件的绘制由UIManager处理
end

function GameOverState.keypressed(key)
    if key == "left" then
        -- 取消当前按钮高亮
        if panel and panel.buttons and panel.buttons[selectedItem] then
            panel.buttons[selectedItem].hovered = false
        end

        selectedItem = selectedItem - 1
        if selectedItem < 1 then
            selectedItem = #(panel.buttons or {})
        end

        -- 高亮新选中的按钮
        if panel and panel.buttons and panel.buttons[selectedItem] then
            panel.buttons[selectedItem].hovered = true
        end
    elseif key == "right" then
        -- 取消当前按钮高亮
        if panel and panel.buttons and panel.buttons[selectedItem] then
            panel.buttons[selectedItem].hovered = false
        end

        selectedItem = selectedItem + 1
        if selectedItem > #(panel.buttons or {}) then
            selectedItem = 1
        end

        -- 高亮新选中的按钮
        if panel and panel.buttons and panel.buttons[selectedItem] then
            panel.buttons[selectedItem].hovered = true
        end
    elseif key == "return" or key == "space" then
        -- 触发当前选中按钮的点击事件
        if panel and panel.buttons and panel.buttons[selectedItem] then
            local button = panel.buttons[selectedItem]
            if button.onClick then
                button.onClick(button)
            end
        end
    elseif key == "escape" then
        StateManager.changeState("menu")
    end
end

function GameOverState.mousemoved(x, y)
    -- 鼠标移动时更新按钮选择
    if panel and panel.buttons then
        for i, button in ipairs(panel.buttons) do
            if button:containsPoint(x, y) then
                -- 取消之前的高亮
                if panel.buttons[selectedItem] then
                    panel.buttons[selectedItem].hovered = false
                end

                selectedItem = i
                button.hovered = true
            end
        end
    end
end

function GameOverState.mousepressed(x, y, button)
    -- 鼠标点击事件由UIManager处理
end

return GameOverState
