-- 光标管理器
-- 负责管理游戏中的光标样式和切换
local CursorManager = {}

-- 私有数据
local cursors = {}
local currentCursor = "arrow"

-- 初始化光标系统
function CursorManager.init()
    print("创建光标...")

    -- 创建系统光标
    cursors.arrow = love.mouse.getSystemCursor("arrow")
    cursors.crosshair = love.mouse.getSystemCursor("crosshair")
    cursors.hand = love.mouse.getSystemCursor("hand")

    print("光标创建完成")

    -- 设置默认光标
    CursorManager.setCursor("arrow")
    print("默认光标设置完成")
end

-- 设置光标
function CursorManager.setCursor(name)
    currentCursor = name
    if cursors[name] then
        love.mouse.setCursor(cursors[name])
    else
        love.mouse.setCursor()
    end
end

-- 获取当前光标名称
function CursorManager.getCurrentCursor()
    return currentCursor
end

-- 获取所有可用光标列表
function CursorManager.getAvailableCursors()
    local list = {}
    for name in pairs(cursors) do
        table.insert(list, name)
    end
    return list
end

-- 检查光标是否存在
function CursorManager.hasCursor(name)
    return cursors[name] ~= nil
end

-- 模块导出（不再需要全局函数包装）

return CursorManager
