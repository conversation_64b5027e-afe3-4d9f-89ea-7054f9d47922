-- 状态管理器
-- 负责管理游戏状态切换和数据保存
local StateManager = {}

-- 当前状态和状态栈
local currentState = nil
local stateStack = {}
local stateData = {}

-- 注册的状态
local states = {}

-- 注册状态
function StateManager.registerState(name, state)
    states[name] = state
end

-- 切换状态
function StateManager.changeState(newStateName, data)
    if currentState and currentState.exit then
        currentState.exit()
    end

    local newState = states[newStateName]
    if not newState then
        error("State not found: " .. newStateName)
    end

    currentState = newState

    if currentState.enter then
        currentState.enter(data)
    end
end

-- 推入状态（用于暂停等需要返回的状态）
function StateManager.pushState(newStateName, data)
    if currentState then
        table.insert(stateStack, {
            state = currentState,
            data = stateData
        })
    end

    StateManager.changeState(newStateName, data)
end

-- 弹出状态（返回到之前的状态）
function StateManager.popState(data)
    if #stateStack > 0 then
        local previousState = table.remove(stateStack)
        local stateName = nil

        -- 查找状态名称
        for name, state in pairs(states) do
            if state == previousState.state then
                stateName = name
                break
            end
        end

        if stateName then
            StateManager.changeState(stateName, data or previousState.data)
        end
    end
end

-- 获取当前状态
function StateManager.getCurrentState()
    return currentState
end

-- 保存状态数据
function StateManager.saveStateData(key, data)
    stateData[key] = data
end

-- 获取状态数据
function StateManager.getStateData(key)
    return stateData[key]
end

-- Love2D回调函数
function StateManager.update(dt)
    if currentState and currentState.update then
        currentState.update(dt)
    end
end

function StateManager.draw()
    if currentState and currentState.draw then
        currentState.draw()
    end
end

function StateManager.keypressed(key)
    if currentState and currentState.keypressed then
        currentState.keypressed(key)
    end
end

function StateManager.keyreleased(key)
    if currentState and currentState.keyreleased then
        currentState.keyreleased(key)
    end
end

function StateManager.mousepressed(x, y, button)
    if currentState and currentState.mousepressed then
        currentState.mousepressed(x, y, button)
    end
end

function StateManager.mousereleased(x, y, button)
    if currentState and currentState.mousereleased then
        currentState.mousereleased(x, y, button)
    end
end

function StateManager.mousemoved(x, y, dx, dy)
    if currentState and currentState.mousemoved then
        currentState.mousemoved(x, y, dx, dy)
    end
end

return StateManager
