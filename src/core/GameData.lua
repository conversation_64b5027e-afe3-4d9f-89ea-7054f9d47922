-- 游戏数据管理器
-- 负责管理所有游戏相关数据
local GameData = {}
local CharacterManager = require("src/entities/characters/CharacterManager")
local WeaponManager = require("src/entities/weapons/WeaponManager")

-- 游戏数据
local gameData = {
    -- 运行数据
    currentRun = {
        playTime = 0,
        level = 1,
        experience = 0,
        experienceToNext = 100,
        gold = 0,
        enemiesKilled = 0,
        damageDealt = 0
    },

    -- 角色数据
    player = {
        selectedCharacter = 1,
        health = 100,
        maxHealth = 100,
        damage = 20,
        speed = 200,
        attackSpeed = 1.0,
        criticalChance = 0.05,
        criticalDamage = 2.0,
        defense = 0,
        magnet = 50,
        recovery = 0,
        area = 1.0,
        duration = 1.0,
        amount = 1,
        pierce = 0,
        cooldown = 1.0,
        might = 1.0,
        revival = 0
    },

    -- 武器和技能
    weapons = {},
    items = {},

    -- 永久升级数据
    persistent = {
        totalGold = 0,
        totalKills = 0,
        totalPlayTime = 0,
        highestLevel = 1,
        unlockedCharacters = {
            [1] = true
        },
        unlockedWeapons = {},
        permanentUpgrades = {}
    },

    -- 游戏选项
    gameMode = "survival",
    difficulty = "normal",

    -- 统计数据
    stats = {
        runsCompleted = 0,
        totalDeaths = 0,
        favoriteWeapon = "",
        bestTime = 0
    }
}

-- 注意：角色和武器数据现在由管理器提供
-- 这里只保留向后兼容的接口

-- 获取游戏数据
function GameData.getData()
    return gameData
end

-- 获取角色数据库（向后兼容）
function GameData.getCharacterDatabase()
    return CharacterManager.getCharacterDatabase()
end

-- 获取武器数据库（向后兼容）
function GameData.getWeaponDatabase()
    return WeaponManager.getWeaponDatabase()
end

-- 重置当前运行数据
function GameData.resetCurrentRun()
    local selectedChar = CharacterManager.getCharacterByIndex(gameData.player.selectedCharacter)

    gameData.currentRun = {
        playTime = 0,
        level = 1,
        experience = 0,
        experienceToNext = 100,
        gold = 0,
        enemiesKilled = 0,
        damageDealt = 0
    }

    -- 重置玩家属性到角色基础值
    gameData.player.health = selectedChar.baseHealth
    gameData.player.maxHealth = selectedChar.baseHealth
    gameData.player.damage = selectedChar.baseDamage
    gameData.player.speed = selectedChar.baseSpeed
    gameData.player.attackSpeed = 1.0
    gameData.player.criticalChance = 0.05
    gameData.player.criticalDamage = 2.0
    gameData.player.defense = 0
    gameData.player.magnet = 50
    gameData.player.recovery = 0
    gameData.player.area = 1.0
    gameData.player.duration = 1.0
    gameData.player.amount = 1
    gameData.player.pierce = 0
    gameData.player.cooldown = 1.0
    gameData.player.might = 1.0
    gameData.player.revival = 0

    -- 清空武器和道具
    gameData.weapons = {}
    gameData.items = {}

    -- 添加起始武器
    GameData.addWeapon(selectedChar.startingWeapon)
end

-- 添加武器
function GameData.addWeapon(weaponId)
    local weaponData = WeaponManager.getWeaponData(weaponId)
    if weaponData then
        table.insert(gameData.weapons, {
            id = weaponId,
            level = 1,
            data = weaponData
        })
    end
end

-- 升级武器
function GameData.upgradeWeapon(weaponIndex)
    if gameData.weapons[weaponIndex] then
        gameData.weapons[weaponIndex].level = gameData.weapons[weaponIndex].level + 1
    end
end

-- 保存游戏数据
function GameData.save()
    -- 可以在这里实现保存到文件的逻辑
end

-- 加载游戏数据
function GameData.load()
    -- 可以在这里实现从文件加载的逻辑
end

return GameData
