-- 本地化管理器
-- 负责管理游戏的多语言支持
local LocaleManager = {}

-- 语言映射
local languageMap = {
    chinese = "zh_CN",
    english = "en_US"
}

-- 缓存的语言包
local locales = {}
local currentLanguage = "chinese"

-- 加载语言包
local function loadLocale(language)
    local localeCode = languageMap[language]
    if not localeCode then
        print("Warning: Unknown language '" .. language .. "', falling back to chinese")
        localeCode = "zh_CN"
        language = "chinese"
    end

    if not locales[language] then
        local success, localeData = pcall(require, "src/locales/" .. localeCode)
        if success then
            locales[language] = localeData
        else
            print("Warning: Failed to load locale '" .. localeCode .. "', using fallback")
            locales[language] = require("src/locales/zh_CN") -- 默认中文
        end
    end

    return locales[language]
end

-- 设置当前语言
function LocaleManager.setLanguage(language)
    currentLanguage = language
    loadLocale(language)
end

-- 获取当前语言
function LocaleManager.getCurrentLanguage()
    return currentLanguage
end

-- 获取文本
function LocaleManager.getText(key)
    local locale = loadLocale(currentLanguage)
    return locale[key] or key
end

-- 切换语言
function LocaleManager.toggleLanguage()
    if currentLanguage == "chinese" then
        LocaleManager.setLanguage("english")
    else
        LocaleManager.setLanguage("chinese")
    end
end

-- 获取支持的语言列表
function LocaleManager.getSupportedLanguages()
    local languages = {}
    for lang, code in pairs(languageMap) do
        table.insert(languages, {
            name = lang,
            code = code,
            displayName = LocaleManager.getText(lang)
        })
    end
    return languages
end

-- 检查语言包是否存在
function LocaleManager.hasLanguage(language)
    return languageMap[language] ~= nil
end

-- 预加载所有语言包
function LocaleManager.preloadAll()
    for language in pairs(languageMap) do
        loadLocale(language)
    end
end

-- 清理缓存
function LocaleManager.cleanup()
    locales = {}
end

-- 初始化（设置默认语言）
function LocaleManager.init(defaultLanguage)
    defaultLanguage = defaultLanguage or "chinese"
    LocaleManager.setLanguage(defaultLanguage)
end

return LocaleManager
