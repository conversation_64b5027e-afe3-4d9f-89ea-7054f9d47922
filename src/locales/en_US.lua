-- English language pack
return {
    -- Menu related
    start_adventure = "Start Adventure",
    character_select = "Character Select",
    game_settings = "Game Settings",
    exit_game = "Exit Game",

    -- Pause menu
    continue_game = "Continue Game",
    return_to_menu = "Return to Menu",
    game_paused = "Game Paused",

    -- Settings
    language = "Language",
    display_mode = "Display Mode",
    volume = "Volume",
    sound_effects = "Sound Effects",
    back = "Back",

    -- Display modes
    windowed = "Windowed",
    fullscreen = "Fullscreen",

    -- Language options
    chinese = "中文",
    english = "English",

    -- Control hints
    keyboard_mouse_select = "↑↓/Mouse Select",
    enter_click_confirm = "Enter/Click Confirm",
    esc_back = "ESC Back",
    esc_continue = "ESC Continue Game",
    f11_fullscreen = "F11 Fullscreen",

    -- Character selection
    select_hero = "Select Your Hero",
    warrior = "Warrior",
    mage = "Mage",
    ranger = "Ranger",
    unlocked = "Locked",

    -- In-game
    health = "Health",
    level = "Level",
    kills = "Kills",
    gold = "Gold",
    time = "Time",
    wave = "Wave",
    weapons = "Weapons",

    -- Game over
    legend_ends = "Legend Ends",
    hero_fallen = "The hero has fallen...",
    reached_level = "Reached Level",
    enemies_defeated = "Enemies Defeated",
    survival_time = "Survival Time",
    gold_earned = "Gold Earned",
    new_level_record = "New Level Record!",
    restart = "Restart",

    -- Upgrade
    level_up = "Level Up!",

    -- Character descriptions
    warrior_desc = "Balanced melee fighter with strong vitality",
    mage_desc = "Fragile but powerful ranged spellcaster",
    ranger_desc = "Fast ranged archer",

    -- Weapon names
    sword = "Sword",
    magic_missile = "Magic Missile",
    bow = "Bow",

    -- Weapon descriptions
    sword_desc = "Basic melee weapon",
    magic_missile_desc = "Homing magic projectile",
    bow_desc = "Shoots fast arrows",

    -- Game title and description
    game_title = "Survivor Legends",
    game_subtitle = "Roguelike Adventure",
    game_description = "Survive endless waves of monsters, collect powerful weapons and skills",

    -- Statistics
    total_kills = "Total Kills",
    highest_level = "Highest Level",

    -- In-game UI
    health = "Health",
    level = "Level",
    experience = "Experience",
    wave = "Wave",
    time = "Time",
    game_controls = "WASD Move    Mouse Aim    LMB Shoot    ESC Pause",

    -- Character selection controls
    character_controls = "← →/Mouse Select Character    Enter/Click Confirm    F11 Fullscreen    ESC Back",

    -- Character status
    locked = "Locked",

    -- Upgrade interface
    choose_upgrade = "Level Up",
    choose_skill_desc = "Choose a skill to enhance your character",
    skip_upgrade = "Skip Upgrade",
    upgrade_controls = "← →/Mouse Select Upgrade    Enter/Click Confirm    ESC Skip",

    -- Upgrade options
    damage_boost_name = "Damage Boost",
    damage_boost_desc = "Increase weapon damage by 25%",
    speed_boost_name = "Speed Boost",
    speed_boost_desc = "Increase movement speed by 20%",
    health_boost_name = "Health Boost",
    health_boost_desc = "Increase max health by 50 points",
    fire_rate_name = "Fire Rate",
    fire_rate_desc = "Increase fire rate by 30%",
    critical_hit_name = "Critical Strike",
    critical_hit_desc = "Increase critical chance by 15%",
    shield_regen_name = "Shield Regen",
    shield_regen_desc = "Regenerate 5 shield per second",
    multi_shot_name = "Multi Shot",
    multi_shot_desc = "Weapons fire multiple projectiles",
    life_steal_name = "Life Steal",
    life_steal_desc = "Restore health when attacking"
}
