-- UI组件基类
-- 提供通用的UI组件功能
local UIComponent = {}
UIComponent.__index = UIComponent

-- 创建新组件
function UIComponent:new(data)
    local instance = {
        x = data.x or 0,
        y = data.y or 0,
        width = data.width or 100,
        height = data.height or 30,
        visible = data.visible ~= false,
        enabled = data.enabled ~= false,

        -- 样式
        backgroundColor = data.backgroundColor or {0.2, 0.2, 0.3, 1},
        borderColor = data.borderColor or {1, 1, 1, 1},
        textColor = data.textColor or {1, 1, 1, 1},
        borderWidth = data.borderWidth or 2,
        cornerRadius = data.cornerRadius or 8,

        -- 文本
        text = data.text or "",
        font = data.font or "medium",

        -- 状态
        hovered = false,
        pressed = false,
        focused = false,

        -- 回调函数
        onClick = data.onClick,
        onHover = data.onHover,
        onFocus = data.onFocus
    }

    setmetatable(instance, UIComponent)
    return instance
end

-- 检查点是否在组件内
function UIComponent:containsPoint(x, y)
    return x >= self.x and x <= self.x + self.width and y >= self.y and y <= self.y + self.height
end

-- 更新组件状态
function UIComponent:update(dt, mouseX, mouseY)
    if not self.visible then
        return
    end

    local wasHovered = self.hovered
    self.hovered = self:containsPoint(mouseX, mouseY)

    if self.hovered and not wasHovered and self.onHover then
        self.onHover(self, true)
    elseif not self.hovered and wasHovered and self.onHover then
        self.onHover(self, false)
    end
end

-- 处理鼠标点击
function UIComponent:handleClick(x, y, button)
    if not self.visible or not self.enabled then
        return false
    end

    if self:containsPoint(x, y) then
        if button == 1 and self.onClick then
            self.onClick(self)
            return true
        end
    end

    return false
end

-- 绘制组件
function UIComponent:draw()
    if not self.visible then
        return
    end

    local bgColor = self.backgroundColor
    local borderColor = self.borderColor

    -- 悬停效果
    if self.hovered and self.enabled then
        bgColor = {math.min(1, bgColor[1] + 0.1), math.min(1, bgColor[2] + 0.1), math.min(1, bgColor[3] + 0.1),
                   bgColor[4]}
    end

    -- 绘制背景
    love.graphics.setColor(bgColor)
    love.graphics.rectangle("fill", self.x, self.y, self.width, self.height, self.cornerRadius)

    -- 绘制边框
    if self.borderWidth > 0 then
        love.graphics.setColor(borderColor)
        love.graphics.setLineWidth(self.borderWidth)
        love.graphics.rectangle("line", self.x, self.y, self.width, self.height, self.cornerRadius)
    end

    -- 绘制文本
    if self.text and self.text ~= "" then
        love.graphics.setColor(self.textColor)
        if _G.FontManager then
            love.graphics.setFont(_G.FontManager.getFont(self.font))
        end

        local textWidth = love.graphics.getFont():getWidth(self.text)
        local textHeight = love.graphics.getFont():getHeight()
        local textX = self.x + (self.width - textWidth) / 2
        local textY = self.y + (self.height - textHeight) / 2

        love.graphics.print(self.text, textX, textY)
    end
end

-- 设置位置
function UIComponent:setPosition(x, y)
    self.x = x
    self.y = y
end

-- 设置尺寸
function UIComponent:setSize(width, height)
    self.width = width
    self.height = height
end

-- 设置文本
function UIComponent:setText(text)
    self.text = text
end

-- 设置可见性
function UIComponent:setVisible(visible)
    self.visible = visible
end

-- 设置启用状态
function UIComponent:setEnabled(enabled)
    self.enabled = enabled
end

return UIComponent
