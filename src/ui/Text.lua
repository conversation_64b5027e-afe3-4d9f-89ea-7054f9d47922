-- Text文本显示组件
-- 专门用于显示文本，支持多种样式和对齐方式
local UIComponent = require("src/ui/UIComponent")
local Text = {}
Text.__index = Text
setmetatable(Text, {
    __index = UIComponent
})

-- 对齐方式枚举
Text.ALIGN = {
    LEFT = "left",
    CENTER = "center",
    RIGHT = "right"
}

-- 创建新文本组件
function Text:new(data)
    local textData = {
        width = data.width or 200,
        height = data.height or 30,
        backgroundColor = data.backgroundColor or {0, 0, 0, 0}, -- 默认透明
        borderWidth = data.borderWidth or 0, -- 默认无边框
        textColor = data.textColor or {1, 1, 1, 1},
        font = data.font or "medium",
        align = data.align or Text.ALIGN.LEFT,
        multiline = data.multiline or false,
        lineSpacing = data.lineSpacing or 1.2
    }

    -- 合并用户数据
    for key, value in pairs(data) do
        textData[key] = value
    end

    local instance = UIComponent:new(textData)
    setmetatable(instance, Text)

    -- 文本特有属性
    instance.align = textData.align
    instance.multiline = textData.multiline
    instance.lineSpacing = textData.lineSpacing
    instance.wrappedText = {}
    instance.shadowColor = data.shadowColor or {0, 0, 0, 0.5}
    instance.shadowOffset = data.shadowOffset or {
        x = 1,
        y = 1
    }
    instance.glowColor = data.glowColor or nil
    instance.glowRadius = data.glowRadius or 2

    -- 如果是多行文本，处理文本换行
    if instance.multiline and instance.text then
        instance:wrapText()
    end

    return instance
end

-- 处理文本换行
function Text:wrapText()
    if not self.multiline or not self.text then
        return
    end

    local font
    if _G.FontManager then
        font = _G.FontManager.getFont(self.font)
    else
        font = love.graphics.getFont()
    end

    local maxWidth = self.width - 20 -- 留一些边距
    self.wrappedText = {}

    local words = {}
    for word in self.text:gmatch("%S+") do
        table.insert(words, word)
    end

    local currentLine = ""
    for _, word in ipairs(words) do
        local testLine = currentLine == "" and word or currentLine .. " " .. word
        if font:getWidth(testLine) <= maxWidth then
            currentLine = testLine
        else
            if currentLine ~= "" then
                table.insert(self.wrappedText, currentLine)
            end
            currentLine = word
        end
    end

    if currentLine ~= "" then
        table.insert(self.wrappedText, currentLine)
    end

    -- 根据行数调整高度
    local lineHeight = font:getHeight() * self.lineSpacing
    self.height = math.max(self.height, #self.wrappedText * lineHeight + 20)
end

-- 绘制文本
function Text:draw()
    if not self.visible or not self.text or self.text == "" then
        return
    end

    -- 绘制背景（如果有）
    if self.backgroundColor[4] > 0 then
        love.graphics.setColor(self.backgroundColor)
        love.graphics.rectangle("fill", self.x, self.y, self.width, self.height, self.cornerRadius)
    end

    -- 绘制边框（如果有）
    if self.borderWidth > 0 then
        love.graphics.setColor(self.borderColor)
        love.graphics.setLineWidth(self.borderWidth)
        love.graphics.rectangle("line", self.x, self.y, self.width, self.height, self.cornerRadius)
    end

    -- 设置字体
    local font
    if _G.FontManager then
        font = _G.FontManager.getFont(self.font)
        love.graphics.setFont(font)
    else
        font = love.graphics.getFont()
    end

    -- 绘制文本
    if self.multiline and #self.wrappedText > 0 then
        self:drawMultilineText(font)
    else
        self:drawSingleLineText(font)
    end
end

-- 绘制单行文本
function Text:drawSingleLineText(font)
    local textWidth = font:getWidth(self.text)
    local textHeight = font:getHeight()

    local textX, textY = self:calculateTextPosition(textWidth, textHeight)

    -- 绘制阴影效果
    if self.shadowColor and self.shadowColor[4] > 0 then
        love.graphics.setColor(self.shadowColor)
        love.graphics.print(self.text, textX + self.shadowOffset.x, textY + self.shadowOffset.y)
    end

    -- 绘制发光效果
    if self.glowColor then
        love.graphics.setColor(self.glowColor[1], self.glowColor[2], self.glowColor[3], 0.3)
        for dx = -self.glowRadius, self.glowRadius do
            for dy = -self.glowRadius, self.glowRadius do
                if dx ~= 0 or dy ~= 0 then
                    love.graphics.print(self.text, textX + dx, textY + dy)
                end
            end
        end
    end

    -- 绘制主文本
    love.graphics.setColor(self.textColor)
    love.graphics.print(self.text, textX, textY)
end

-- 绘制多行文本
function Text:drawMultilineText(font)
    local lineHeight = font:getHeight() * self.lineSpacing
    local totalHeight = #self.wrappedText * lineHeight
    local startY = self.y + (self.height - totalHeight) / 2

    for i, line in ipairs(self.wrappedText) do
        local lineWidth = font:getWidth(line)
        local lineX = self:calculateLineX(lineWidth)
        local lineY = startY + (i - 1) * lineHeight

        -- 绘制阴影
        if self.shadowColor and self.shadowColor[4] > 0 then
            love.graphics.setColor(self.shadowColor)
            love.graphics.print(line, lineX + self.shadowOffset.x, lineY + self.shadowOffset.y)
        end

        -- 绘制发光效果
        if self.glowColor then
            love.graphics.setColor(self.glowColor[1], self.glowColor[2], self.glowColor[3], 0.3)
            for dx = -self.glowRadius, self.glowRadius do
                for dy = -self.glowRadius, self.glowRadius do
                    if dx ~= 0 or dy ~= 0 then
                        love.graphics.print(line, lineX + dx, lineY + dy)
                    end
                end
            end
        end

        -- 绘制主文本
        love.graphics.setColor(self.textColor)
        love.graphics.print(line, lineX, lineY)
    end
end

-- 计算文本位置
function Text:calculateTextPosition(textWidth, textHeight)
    local textX = self:calculateLineX(textWidth)
    local textY = self.y + (self.height - textHeight) / 2
    return textX, textY
end

-- 计算行的X位置（根据对齐方式）
function Text:calculateLineX(lineWidth)
    if self.align == Text.ALIGN.CENTER then
        return self.x + (self.width - lineWidth) / 2
    elseif self.align == Text.ALIGN.RIGHT then
        return self.x + self.width - lineWidth - 10
    else -- LEFT
        return self.x + 10
    end
end

-- 设置文本内容并重新处理换行
function Text:setText(text)
    UIComponent.setText(self, text)
    if self.multiline then
        self:wrapText()
    end
end

-- 设置文本颜色
function Text:setTextColor(r, g, b, a)
    self.textColor = {r, g, b, a or 1}
end

-- 设置字体
function Text:setFont(font)
    self.font = font
    if self.multiline then
        self:wrapText()
    end
end

-- 设置对齐方式
function Text:setAlign(align)
    self.align = align
end

-- 创建标题样式文本
function Text.createTitle(data)
    data = data or {}
    data.font = data.font or "title"
    data.textColor = data.textColor or {0.9, 0.7, 1, 1} -- 魔法紫色
    data.align = data.align or Text.ALIGN.CENTER
    data.glowColor = data.glowColor or {0.9, 0.7, 1, 0.5}
    return Text:new(data)
end

-- 创建副标题样式文本
function Text.createSubtitle(data)
    data = data or {}
    data.font = data.font or "large"
    data.textColor = data.textColor or {0.7, 0.8, 0.9, 1}
    data.align = data.align or Text.ALIGN.CENTER
    return Text:new(data)
end

-- 创建描述文本样式
function Text.createDescription(data)
    data = data or {}
    data.font = data.font or "medium"
    data.textColor = data.textColor or {0.6, 0.6, 0.7, 1}
    data.align = data.align or Text.ALIGN.CENTER
    data.multiline = data.multiline ~= false
    return Text:new(data)
end

-- 创建说明文本样式
function Text.createHelp(data)
    data = data or {}
    data.font = data.font or "small"
    data.textColor = data.textColor or {0.5, 0.5, 0.6, 1}
    data.align = data.align or Text.ALIGN.LEFT
    data.multiline = data.multiline ~= false
    return Text:new(data)
end

return Text
