-- UI字体管理器
-- 负责管理游戏中的所有字体资源
local FontManager = {}

-- 字体缓存
local fonts = {}

-- 字体配置
local fontConfig = {
    fontPath = "assets/fonts/LXGWMarkerGothic-Regular.ttf",
    sizes = {
        small = 14,
        normal = 16,
        medium = 20,
        large = 24,
        title = 36,
        huge = 48
    }
}

-- 加载字体（私有函数）
local function loadFont(size)
    local success, font = pcall(love.graphics.newFont, fontConfig.fontPath, size)
    if success then
        return font
    else
        print("Warning: Failed to load custom font, using default font for size " .. size)
        return love.graphics.newFont(size)
    end
end

-- 获取字体
function FontManager.getFont(sizeName)
    local size = fontConfig.sizes[sizeName] or sizeName

    if not fonts[size] then
        fonts[size] = loadFont(size)
    end

    return fonts[size]
end

-- 预加载所有字体
function FontManager.preloadFonts()
    for sizeName, size in pairs(fontConfig.sizes) do
        FontManager.getFont(sizeName)
    end
end

-- 清理字体缓存
function FontManager.cleanup()
    fonts = {}
end

-- 检查自定义字体是否可用
function FontManager.isCustomFontAvailable()
    local success = pcall(love.graphics.newFont, fontConfig.fontPath, 16)
    return success
end

-- 获取字体配置信息
function FontManager.getFontInfo()
    return {
        path = fontConfig.fontPath,
        available = FontManager.isCustomFontAvailable(),
        sizes = fontConfig.sizes
    }
end

-- 设置字体路径（用于运行时更换字体）
function FontManager.setFontPath(newPath)
    fontConfig.fontPath = newPath
    FontManager.cleanup() -- 清理缓存以重新加载
end

-- 获取所有可用字体尺寸
function FontManager.getAvailableSizes()
    local sizes = {}
    for name, size in pairs(fontConfig.sizes) do
        table.insert(sizes, {
            name = name,
            size = size
        })
    end
    return sizes
end

return FontManager
