-- 按钮UI组件
-- 提供统一的按钮样式和行为
local UIComponent = require("src/ui/UIComponent")
local Button = {}
Button.__index = Button
setmetatable(Button, {
    __index = UIComponent
})

-- 创建新按钮
function Button:new(data)
    -- 默认按钮样式
    local buttonData = {
        width = data.width or 200,
        height = data.height or 44,
        backgroundColor = data.backgroundColor or {0.2, 0.3, 0.4, 0.8},
        borderColor = data.borderColor or {0.5, 0.8, 1, 1},
        textColor = data.textColor or {1, 1, 1, 1},
        borderWidth = data.borderWidth or 2,
        cornerRadius = data.cornerRadius or 8,
        font = data.font or "medium"
    }

    -- 合并用户提供的数据
    for key, value in pairs(data) do
        buttonData[key] = value
    end

    local instance = UIComponent:new(buttonData)
    setmetatable(instance, Button)

    -- 按钮特有属性
    instance.pressedColor = data.pressedColor or {0.1, 0.2, 0.3, 0.9}
    instance.disabledColor = data.disabledColor or {0.1, 0.1, 0.1, 0.5}
    instance.hoveredColor = data.hoveredColor or {0.3, 0.4, 0.5, 0.9}

    return instance
end

-- 重写绘制方法，添加按钮特有的视觉效果
function Button:draw()
    if not self.visible then
        return
    end

    local bgColor = self.backgroundColor
    local borderColor = self.borderColor
    local textColor = self.textColor

    -- 根据状态调整颜色
    if not self.enabled then
        bgColor = self.disabledColor
        textColor = {0.5, 0.5, 0.5, 1}
    elseif self.pressed then
        bgColor = self.pressedColor
    elseif self.hovered then
        bgColor = self.hoveredColor
        borderColor = {math.min(1, borderColor[1] + 0.2), math.min(1, borderColor[2] + 0.2),
                       math.min(1, borderColor[3] + 0.2), borderColor[4]}
    end

    -- 绘制背景
    love.graphics.setColor(bgColor)
    love.graphics.rectangle("fill", self.x, self.y, self.width, self.height, self.cornerRadius)

    -- 绘制边框
    if self.borderWidth > 0 then
        love.graphics.setColor(borderColor)
        love.graphics.setLineWidth(self.borderWidth)
        love.graphics.rectangle("line", self.x, self.y, self.width, self.height, self.cornerRadius)
    end

    -- 绘制发光效果（选中时）
    if self.hovered and self.enabled then
        love.graphics.setColor(borderColor[1], borderColor[2], borderColor[3], 0.3)
        love.graphics.setLineWidth(4)
        love.graphics.rectangle("line", self.x - 2, self.y - 2, self.width + 4, self.height + 4, self.cornerRadius + 2)
    end

    -- 绘制文本
    if self.text and self.text ~= "" then
        love.graphics.setColor(textColor)
        if _G.FontManager then
            love.graphics.setFont(_G.FontManager.getFont(self.font))
        end

        local font = love.graphics.getFont()
        local textWidth = font:getWidth(self.text)
        local textHeight = font:getHeight()
        local textX = self.x + (self.width - textWidth) / 2
        local textY = self.y + (self.height - textHeight) / 2

        -- 按下时文本稍微偏移
        if self.pressed then
            textX = textX + 1
            textY = textY + 1
        end

        love.graphics.print(self.text, textX, textY)
    end
end

-- 重写点击处理，添加按下效果
function Button:handleClick(x, y, button)
    if not self.visible or not self.enabled then
        return false
    end

    if self:containsPoint(x, y) and button == 1 then
        self.pressed = true
        if self.onClick then
            self.onClick(self)
        end
        return true
    end

    return false
end

-- 处理鼠标释放
function Button:handleRelease(x, y, button)
    if button == 1 then
        self.pressed = false
    end
end

-- 创建预设样式的按钮
function Button.createPrimary(data)
    data = data or {}
    data.backgroundColor = data.backgroundColor or {0.3, 0.6, 0.9, 0.8}
    data.borderColor = data.borderColor or {0.5, 0.8, 1, 1}
    data.hoveredColor = data.hoveredColor or {0.4, 0.7, 1, 0.9}
    data.pressedColor = data.pressedColor or {0.2, 0.5, 0.8, 0.9}
    return Button:new(data)
end

function Button.createSecondary(data)
    data = data or {}
    data.backgroundColor = data.backgroundColor or {0.4, 0.4, 0.4, 0.8}
    data.borderColor = data.borderColor or {0.7, 0.7, 0.7, 1}
    data.hoveredColor = data.hoveredColor or {0.5, 0.5, 0.5, 0.9}
    data.pressedColor = data.pressedColor or {0.3, 0.3, 0.3, 0.9}
    return Button:new(data)
end

function Button.createDanger(data)
    data = data or {}
    data.backgroundColor = data.backgroundColor or {0.8, 0.2, 0.2, 0.8}
    data.borderColor = data.borderColor or {1, 0.3, 0.3, 1}
    data.hoveredColor = data.hoveredColor or {0.9, 0.3, 0.3, 0.9}
    data.pressedColor = data.pressedColor or {0.7, 0.1, 0.1, 0.9}
    return Button:new(data)
end

return Button
