-- Card卡片组件
-- 专门用于角色选择、物品展示等卡片式UI
local UIComponent = require("src/ui/UIComponent")
local Card = {}
Card.__index = Card
setmetatable(Card, {
    __index = UIComponent
})

-- 创建新卡片
function Card:new(data)
    local cardData = {
        width = data.width or 200,
        height = data.height or 280,
        backgroundColor = data.backgroundColor or {0.2, 0.3, 0.4, 0.8},
        borderColor = data.borderColor or {1, 1, 1, 1},
        borderWidth = data.borderWidth or 2,
        cornerRadius = data.cornerRadius or 8,
        padding = data.padding or 15,

        -- 卡片特有属性
        title = data.title or "",
        description = data.description or "",
        image = data.image or nil,
        imageWidth = data.imageWidth or 64,
        imageHeight = data.imageHeight or 64,

        -- 状态
        selectable = data.selectable ~= false,
        selected = data.selected or false,
        locked = data.locked or false,

        -- 字体
        titleFont = data.titleFont or "medium",
        descFont = data.descFont or "small"
    }

    -- 合并用户数据
    for key, value in pairs(data) do
        cardData[key] = value
    end

    local instance = UIComponent:new(cardData)
    setmetatable(instance, Card)

    -- 卡片特有属性
    instance.title = cardData.title
    instance.description = cardData.description
    instance.image = cardData.image
    instance.imageWidth = cardData.imageWidth
    instance.imageHeight = cardData.imageHeight
    instance.selectable = cardData.selectable
    instance.selected = cardData.selected
    instance.locked = cardData.locked
    instance.titleFont = cardData.titleFont
    instance.descFont = cardData.descFont
    instance.padding = cardData.padding

    -- 状态颜色
    instance.selectedColor = data.selectedColor or {0.3, 0.6, 0.9, 0.8}
    instance.lockedColor = data.lockedColor or {0.1, 0.1, 0.1, 0.8}
    instance.hoveredColor = data.hoveredColor or {0.3, 0.4, 0.5, 0.9}

    return instance
end

-- 绘制卡片
function Card:draw()
    if not self.visible then
        return
    end

    local bgColor = self.backgroundColor
    local borderColor = self.borderColor
    local textColor = self.textColor

    -- 根据状态调整颜色
    if self.locked then
        bgColor = self.lockedColor
        textColor = {0.5, 0.5, 0.5, 1}
        borderColor = {0.3, 0.3, 0.3, 1}
    elseif self.selected then
        bgColor = self.selectedColor
        borderColor = {0.5, 0.8, 1, 1}
    elseif self.hovered and not self.locked then
        bgColor = self.hoveredColor
        borderColor = {math.min(1, borderColor[1] + 0.2), math.min(1, borderColor[2] + 0.2),
                       math.min(1, borderColor[3] + 0.2), borderColor[4]}
    end

    -- 绘制卡片背景
    love.graphics.setColor(bgColor)
    love.graphics.rectangle("fill", self.x, self.y, self.width, self.height, self.cornerRadius)

    -- 绘制卡片边框
    if self.borderWidth > 0 then
        love.graphics.setColor(borderColor)
        love.graphics.setLineWidth(self.borderWidth)
        love.graphics.rectangle("line", self.x, self.y, self.width, self.height, self.cornerRadius)
    end

    -- 绘制选中发光效果
    if self.selected and not self.locked then
        love.graphics.setColor(0.5, 0.8, 1, 0.4)
        love.graphics.setLineWidth(4)
        love.graphics.rectangle("line", self.x - 2, self.y - 2, self.width + 4, self.height + 4, self.cornerRadius + 2)
    end

    -- 绘制内容
    if self.locked then
        self:drawLockedContent()
    else
        self:drawContent()
    end
end

-- 绘制正常内容
function Card:drawContent()
    local currentY = self.y + self.padding

    -- 绘制图像（如果有）
    if self.image then
        currentY = self:drawImage(currentY)
    end

    -- 绘制标题
    if self.title and self.title ~= "" then
        currentY = self:drawTitle(currentY)
    end

    -- 绘制描述
    if self.description and self.description ~= "" then
        self:drawDescription(currentY)
    end
end

-- 绘制锁定状态内容
function Card:drawLockedContent()
    love.graphics.setColor(0.5, 0.5, 0.5, 1)

    if _G.FontManager then
        love.graphics.setFont(_G.FontManager.getFont("medium"))
    end

    local lockText = "🔒 " .. LocaleManager.getText("locked")
    local textWidth = love.graphics.getFont():getWidth(lockText)
    local textX = self.x + (self.width - textWidth) / 2
    local textY = self.y + self.height / 2 - 10

    love.graphics.print(lockText, textX, textY)

    -- 绘制解锁条件（如果有）
    if self.unlockCondition then
        if _G.FontManager then
            love.graphics.setFont(_G.FontManager.getFont("small"))
        end

        love.graphics.setColor(0.4, 0.4, 0.4, 1)
        local condText = self.unlockCondition
        local condWidth = love.graphics.getFont():getWidth(condText)
        local condX = self.x + (self.width - condWidth) / 2
        local condY = textY + 30

        love.graphics.print(condText, condX, condY)
    end
end

-- 绘制图像
function Card:drawImage(startY)
    -- 这里可以扩展为真正的图像绘制
    -- 目前绘制一个占位符矩形，确保居中
    local imageX = self.x + (self.width - self.imageWidth) / 2
    local imageY = startY

    love.graphics.setColor(0.4, 0.4, 0.5, 1)
    love.graphics.rectangle("fill", imageX, imageY, self.imageWidth, self.imageHeight, 8)

    love.graphics.setColor(0.7, 0.7, 0.8, 1)
    love.graphics.setLineWidth(1)
    love.graphics.rectangle("line", imageX, imageY, self.imageWidth, self.imageHeight, 8)

    return startY + self.imageHeight + 10 -- 减少间距
end

-- 绘制标题
function Card:drawTitle(startY)
    if _G.FontManager then
        love.graphics.setFont(_G.FontManager.getFont(self.titleFont))
    end

    love.graphics.setColor(self.textColor)
    local titleWidth = love.graphics.getFont():getWidth(self.title)
    local titleX = self.x + (self.width - titleWidth) / 2
    local titleY = startY

    love.graphics.print(self.title, titleX, titleY)

    return startY + love.graphics.getFont():getHeight() + 8 -- 减少间距
end

-- 绘制描述
function Card:drawDescription(startY)
    if _G.FontManager then
        love.graphics.setFont(_G.FontManager.getFont(self.descFont))
    end

    love.graphics.setColor(0.8, 0.8, 0.8, 1)

    -- 改进的文本换行，支持中文和英文混合
    local font = love.graphics.getFont()
    local maxWidth = self.width - self.padding * 2
    local availableHeight = self.height - startY + self.y - self.padding
    local lineHeight = font:getHeight() + 2
    local maxLines = math.floor(availableHeight / lineHeight)

    local lines = {}
    local currentLine = ""

    -- 优化的文本换行，支持单词和中文混合
    local words = {}
    local currentWord = ""

    -- 将文本分解为单词和中文字符
    local i = 1
    while i <= #self.description do
        local byte = string.byte(self.description, i)
        local char
        local charWidth

        if byte > 127 then
            -- 中文字符（多字节）
            if byte >= 240 then
                charWidth = 4
            elseif byte >= 224 then
                charWidth = 3
            elseif byte >= 192 then
                charWidth = 2
            else
                charWidth = 1
            end
            char = self.description:sub(i, i + charWidth - 1)

            -- 添加当前单词（如果有）
            if currentWord ~= "" then
                table.insert(words, currentWord)
                currentWord = ""
            end
            -- 添加中文字符
            table.insert(words, char)
        else
            -- 英文字符
            charWidth = 1
            char = self.description:sub(i, i)

            if char:match("%s") then
                -- 空格，结束当前单词
                if currentWord ~= "" then
                    table.insert(words, currentWord)
                    currentWord = ""
                end
                table.insert(words, char)
            else
                -- 字母，添加到当前单词
                currentWord = currentWord .. char
            end
        end

        i = i + charWidth
    end

    -- 添加最后的单词
    if currentWord ~= "" then
        table.insert(words, currentWord)
    end

    -- 组合单词成行
    for _, word in ipairs(words) do
        local testLine = currentLine == "" and word or currentLine .. word

        if font:getWidth(testLine) <= maxWidth then
            currentLine = testLine
        else
            -- 当前行已满
            if currentLine ~= "" then
                table.insert(lines, currentLine)
                if #lines >= maxLines then
                    -- 如果超过最大行数，添加省略号
                    lines[#lines] = lines[#lines]:sub(1, -4) .. "..."
                    break
                end
            end
            currentLine = word
        end
    end

    if currentLine ~= "" and #lines < maxLines then
        table.insert(lines, currentLine)
    end

    -- 绘制每一行，居中对齐
    for i, line in ipairs(lines) do
        local lineWidth = font:getWidth(line)
        local lineX = self.x + (self.width - lineWidth) / 2
        local lineY = startY + (i - 1) * lineHeight
        love.graphics.print(line, lineX, lineY)
    end
end

-- 处理点击
function Card:handleClick(x, y, button)
    if not self.visible or self.locked then
        return false
    end

    if self:containsPoint(x, y) and button == 1 then
        if self.selectable then
            self.selected = not self.selected
        end

        if self.onClick then
            self.onClick(self)
        end
        return true
    end

    return false
end

-- 设置选中状态
function Card:setSelected(selected)
    if not self.locked and self.selectable then
        self.selected = selected
    end
end

-- 设置锁定状态
function Card:setLocked(locked)
    self.locked = locked
    if locked then
        self.selected = false
    end
end

-- 设置标题
function Card:setTitle(title)
    self.title = title
end

-- 设置描述
function Card:setDescription(description)
    self.description = description
end

-- 设置解锁条件文本
function Card:setUnlockCondition(condition)
    self.unlockCondition = condition
end

-- 创建角色卡片样式
function Card.createCharacter(data)
    data = data or {}
    data.backgroundColor = data.backgroundColor or {0.2, 0.3, 0.4, 0.8}
    data.selectedColor = data.selectedColor or {0.3, 0.6, 0.9, 0.8}
    data.hoveredColor = data.hoveredColor or {0.3, 0.4, 0.5, 0.9}
    data.titleFont = data.titleFont or "medium"
    data.descFont = data.descFont or "small"
    return Card:new(data)
end

-- 创建物品卡片样式
function Card.createItem(data)
    data = data or {}
    data.width = data.width or 120
    data.height = data.height or 150
    data.backgroundColor = data.backgroundColor or {0.3, 0.2, 0.4, 0.8}
    data.titleFont = data.titleFont or "small"
    data.descFont = data.descFont or "small"
    return Card:new(data)
end

-- 创建技能卡片样式
function Card.createSkill(data)
    data = data or {}
    data.backgroundColor = data.backgroundColor or {0.4, 0.2, 0.3, 0.8}
    data.selectedColor = data.selectedColor or {0.8, 0.3, 0.4, 0.8}
    data.titleFont = data.titleFont or "medium"
    return Card:new(data)
end

return Card
