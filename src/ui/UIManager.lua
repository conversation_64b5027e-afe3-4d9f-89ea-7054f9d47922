-- UI管理器
-- 统一管理所有UI组件的更新、绘制和事件处理
local UIManager = {}

-- UI组件注册表
local components = {}
local updateComponents = {}
local drawComponents = {}

-- 鼠标状态
local mouseX, mouseY = 0, 0
local mousePressed = false

-- 初始化UI管理器
function UIManager.init()
    components = {}
    updateComponents = {}
    drawComponents = {}
    mouseX, mouseY = love.mouse.getPosition()
end

-- 添加UI组件
function UIManager.addComponent(component, layer)
    layer = layer or 0

    -- 确保layer存在
    if not components[layer] then
        components[layer] = {}
        updateComponents[layer] = {}
        drawComponents[layer] = {}
    end

    table.insert(components[layer], component)

    -- 如果组件有update方法，添加到更新列表
    if component.update then
        table.insert(updateComponents[layer], component)
    end

    -- 如果组件有draw方法，添加到绘制列表
    if component.draw then
        table.insert(drawComponents[layer], component)
    end
end

-- 移除UI组件
function UIManager.removeComponent(component)
    for layer, layerComponents in pairs(components) do
        for i, comp in ipairs(layerComponents) do
            if comp == component then
                table.remove(layerComponents, i)
                break
            end
        end

        -- 从更新列表移除
        for i, comp in ipairs(updateComponents[layer] or {}) do
            if comp == component then
                table.remove(updateComponents[layer], i)
                break
            end
        end

        -- 从绘制列表移除
        for i, comp in ipairs(drawComponents[layer] or {}) do
            if comp == component then
                table.remove(drawComponents[layer], i)
                break
            end
        end
    end
end

-- 清空所有组件
function UIManager.clear()
    components = {}
    updateComponents = {}
    drawComponents = {}
end

-- 清空指定层的组件
function UIManager.clearLayer(layer)
    components[layer] = {}
    updateComponents[layer] = {}
    drawComponents[layer] = {}
end

-- 更新所有UI组件
function UIManager.update(dt)
    mouseX, mouseY = love.mouse.getPosition()

    -- 按layer顺序更新
    local layers = {}
    for layer in pairs(updateComponents) do
        table.insert(layers, layer)
    end
    table.sort(layers)

    for _, layer in ipairs(layers) do
        for _, component in ipairs(updateComponents[layer]) do
            if component.visible ~= false then
                component:update(dt, mouseX, mouseY)
            end
        end
    end
end

-- 绘制所有UI组件
function UIManager.draw()
    -- 按layer顺序绘制（layer越小越先绘制，即越在底层）
    local layers = {}
    for layer in pairs(drawComponents) do
        table.insert(layers, layer)
    end
    table.sort(layers)

    for _, layer in ipairs(layers) do
        for _, component in ipairs(drawComponents[layer]) do
            if component.visible ~= false then
                component:draw()
            end
        end
    end
end

-- 处理鼠标按下事件
function UIManager.mousepressed(x, y, button)
    mousePressed = true

    -- 从最高layer开始处理，一旦有组件处理了事件就停止
    local layers = {}
    for layer in pairs(components) do
        table.insert(layers, layer)
    end
    table.sort(layers, function(a, b)
        return a > b
    end) -- 降序，高layer先处理

    for _, layer in ipairs(layers) do
        -- 从后往前遍历（后添加的组件在前面）
        for i = #components[layer], 1, -1 do
            local component = components[layer][i]
            if component.handleClick and component:handleClick(x, y, button) then
                return true -- 事件被处理，停止传播
            end
        end
    end

    return false
end

-- 处理鼠标释放事件
function UIManager.mousereleased(x, y, button)
    mousePressed = false

    -- 通知所有组件鼠标释放
    for layer, layerComponents in pairs(components) do
        for _, component in ipairs(layerComponents) do
            if component.handleRelease then
                component:handleRelease(x, y, button)
            end
        end
    end
end

-- 处理鼠标移动事件
function UIManager.mousemoved(x, y, dx, dy)
    mouseX, mouseY = x, y

    -- 通知所有组件鼠标移动
    for layer, layerComponents in pairs(components) do
        for _, component in ipairs(layerComponents) do
            if component.handleMouseMove then
                component:handleMouseMove(x, y, dx, dy)
            end
        end
    end
end

-- 处理键盘按下事件
function UIManager.keypressed(key)
    -- 从最高layer开始处理
    local layers = {}
    for layer in pairs(components) do
        table.insert(layers, layer)
    end
    table.sort(layers, function(a, b)
        return a > b
    end)

    for _, layer in ipairs(layers) do
        for i = #components[layer], 1, -1 do
            local component = components[layer][i]
            if component.handleKeyPress and component:handleKeyPress(key) then
                return true -- 事件被处理
            end
        end
    end

    return false
end

-- 获取指定层的所有组件
function UIManager.getLayerComponents(layer)
    return components[layer] or {}
end

-- 获取所有组件
function UIManager.getAllComponents()
    local allComponents = {}
    for layer, layerComponents in pairs(components) do
        for _, component in ipairs(layerComponents) do
            table.insert(allComponents, {
                component = component,
                layer = layer
            })
        end
    end
    return allComponents
end

-- 查找组件
function UIManager.findComponent(predicate)
    for layer, layerComponents in pairs(components) do
        for _, component in ipairs(layerComponents) do
            if predicate(component) then
                return component, layer
            end
        end
    end
    return nil
end

-- 查找所有匹配的组件
function UIManager.findAllComponents(predicate)
    local results = {}
    for layer, layerComponents in pairs(components) do
        for _, component in ipairs(layerComponents) do
            if predicate(component) then
                table.insert(results, {
                    component = component,
                    layer = layer
                })
            end
        end
    end
    return results
end

-- 设置组件可见性
function UIManager.setComponentVisible(component, visible)
    component.visible = visible
end

-- 设置组件启用状态
function UIManager.setComponentEnabled(component, enabled)
    component.enabled = enabled
end

-- 设置层可见性
function UIManager.setLayerVisible(layer, visible)
    local layerComponents = components[layer] or {}
    for _, component in ipairs(layerComponents) do
        component.visible = visible
    end
end

-- 设置层启用状态
function UIManager.setLayerEnabled(layer, enabled)
    local layerComponents = components[layer] or {}
    for _, component in ipairs(layerComponents) do
        component.enabled = enabled
    end
end

-- 获取当前鼠标位置
function UIManager.getMousePosition()
    return mouseX, mouseY
end

-- 检查鼠标是否按下
function UIManager.isMousePressed()
    return mousePressed
end

-- 调试信息
function UIManager.getDebugInfo()
    local info = {
        totalComponents = 0,
        layers = {}
    }

    for layer, layerComponents in pairs(components) do
        local layerInfo = {
            layer = layer,
            componentCount = #layerComponents,
            updateCount = #(updateComponents[layer] or {}),
            drawCount = #(drawComponents[layer] or {})
        }
        table.insert(info.layers, layerInfo)
        info.totalComponents = info.totalComponents + #layerComponents
    end

    table.sort(info.layers, function(a, b)
        return a.layer < b.layer
    end)

    return info
end

return UIManager
