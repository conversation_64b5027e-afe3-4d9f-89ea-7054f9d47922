-- Panel面板容器组件
-- 用于组织和布局其他UI组件
local UIComponent = require("src/ui/UIComponent")
local Panel = {}
Panel.__index = Panel
setmetatable(Panel, {
    __index = UIComponent
})

-- 创建新面板
function Panel:new(data)
    local panelData = {
        width = data.width or 400,
        height = data.height or 300,
        backgroundColor = data.backgroundColor or {0.15, 0.15, 0.25, 0.9},
        borderColor = data.borderColor or {1, 1, 1, 1},
        borderWidth = data.borderWidth or 3,
        cornerRadius = data.cornerRadius or 15,
        padding = data.padding or 20,
        title = data.title or "",
        titleFont = data.titleFont or "large"
    }

    -- 合并用户数据
    for key, value in pairs(data) do
        panelData[key] = value
    end

    local instance = UIComponent:new(panelData)
    setmetatable(instance, Panel)

    -- 面板特有属性
    instance.children = {}
    instance.padding = panelData.padding
    instance.title = panelData.title
    instance.titleFont = panelData.titleFont
    instance.contentY = panelData.y + (panelData.title ~= "" and 60 or panelData.padding)

    return instance
end

-- 添加子组件
function Panel:addChild(child, x, y)
    if x and y then
        child:setPosition(self.x + self.padding + x, self.contentY + y)
    else
        child:setPosition(self.x + self.padding, self.contentY + #self.children * (child.height + 10))
    end
    table.insert(self.children, child)
end

-- 移除子组件
function Panel:removeChild(child)
    for i, c in ipairs(self.children) do
        if c == child then
            table.remove(self.children, i)
            break
        end
    end
end

-- 清空所有子组件
function Panel:clearChildren()
    self.children = {}
end

-- 更新面板和所有子组件
function Panel:update(dt, mouseX, mouseY)
    UIComponent.update(self, dt, mouseX, mouseY)

    for _, child in ipairs(self.children) do
        if child.update then
            child:update(dt, mouseX, mouseY)
        end
    end
end

-- 处理点击事件，传递给子组件
function Panel:handleClick(x, y, button)
    -- 先让子组件处理
    for i = #self.children, 1, -1 do
        local child = self.children[i]
        if child.handleClick and child:handleClick(x, y, button) then
            return true
        end
    end

    -- 如果子组件没有处理，则面板自己处理
    return UIComponent.handleClick(self, x, y, button)
end

-- 处理鼠标释放，传递给子组件
function Panel:handleRelease(x, y, button)
    for _, child in ipairs(self.children) do
        if child.handleRelease then
            child:handleRelease(x, y, button)
        end
    end
end

-- 绘制面板和所有子组件
function Panel:draw()
    if not self.visible then
        return
    end

    -- 绘制面板背景
    love.graphics.setColor(self.backgroundColor)
    love.graphics.rectangle("fill", self.x, self.y, self.width, self.height, self.cornerRadius)

    -- 绘制面板边框
    if self.borderWidth > 0 then
        love.graphics.setColor(self.borderColor)
        love.graphics.setLineWidth(self.borderWidth)
        love.graphics.rectangle("line", self.x, self.y, self.width, self.height, self.cornerRadius)
    end

    -- 绘制标题
    if self.title and self.title ~= "" then
        love.graphics.setColor(1, 1, 1, 1)
        if _G.FontManager then
            love.graphics.setFont(_G.FontManager.getFont(self.titleFont))
        end

        local titleWidth = love.graphics.getFont():getWidth(self.title)
        local titleX = self.x + (self.width - titleWidth) / 2
        local titleY = self.y + 30
        love.graphics.print(self.title, titleX, titleY)
    end

    -- 绘制所有子组件
    for _, child in ipairs(self.children) do
        if child.draw then
            child:draw()
        end
    end
end

-- 居中显示面板
function Panel:center()
    local screenWidth = love.graphics.getWidth()
    local screenHeight = love.graphics.getHeight()
    self:setPosition((screenWidth - self.width) / 2, (screenHeight - self.height) / 2)

    -- 重新计算子组件位置
    self.contentY = self.y + (self.title ~= "" and 60 or self.padding)
    for i, child in ipairs(self.children) do
        child:setPosition(self.x + self.padding, self.contentY + (i - 1) * (child.height + 10))
    end
end

-- 设置面板位置，同时更新子组件位置
function Panel:setPosition(x, y)
    local deltaX = x - self.x
    local deltaY = y - self.y

    UIComponent.setPosition(self, x, y)
    self.contentY = self.y + (self.title ~= "" and 60 or self.padding)

    -- 更新所有子组件位置
    for _, child in ipairs(self.children) do
        child:setPosition(child.x + deltaX, child.y + deltaY)
    end
end

-- 创建模态对话框样式的面板
function Panel.createModal(data)
    data = data or {}
    data.backgroundColor = data.backgroundColor or {0.15, 0.15, 0.25, 1}
    data.borderColor = data.borderColor or {1, 1, 1, 1}
    data.borderWidth = data.borderWidth or 3
    data.cornerRadius = data.cornerRadius or 15

    local panel = Panel:new(data)
    panel:center()
    return panel
end

-- 创建游戏窗口样式的面板
function Panel.createWindow(data)
    data = data or {}
    data.backgroundColor = data.backgroundColor or {0.1, 0.1, 0.15, 0.95}
    data.borderColor = data.borderColor or {0.8, 0.8, 0.9, 1}
    data.borderWidth = data.borderWidth or 2
    data.cornerRadius = data.cornerRadius or 10

    return Panel:new(data)
end

return Panel
