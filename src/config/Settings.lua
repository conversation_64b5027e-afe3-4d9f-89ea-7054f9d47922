-- 设置管理器
-- 负责管理游戏设置，包括语言、显示模式等
local Settings = {}
local LocaleManager = require("src/locales/LocaleManager")

-- 设置数据
local settings = {
    language = "chinese",
    displayMode = "windowed", -- "windowed", "fullscreen"
    volume = 1.0,
    sfxVolume = 1.0,
    vsync = true
}

-- 获取设置
function Settings.get(key)
    return settings[key]
end

-- 设置值
function Settings.set(key, value)
    settings[key] = value
end

-- 获取文本
function Settings.getText(key)
    return LocaleManager.getText(key)
end

-- 切换语言
function Settings.toggleLanguage()
    if settings.language == "chinese" then
        settings.language = "english"
    else
        settings.language = "chinese"
    end
    LocaleManager.setLanguage(settings.language)
end

-- 切换显示模式
function Settings.toggleDisplayMode()
    if settings.displayMode == "windowed" then
        settings.displayMode = "fullscreen"
        love.window.setFullscreen(true, "desktop")
    else
        settings.displayMode = "windowed"
        love.window.setFullscreen(false)
        -- 直接最大化窗口（自适应所有屏幕尺寸）
        love.window.maximize()
    end
end

-- 获取当前显示模式
function Settings.isFullscreen()
    return settings.displayMode == "fullscreen"
end

-- 辅助函数：将表格转换为字符串
local function tableToString(t)
    local result = "{\n"
    for key, value in pairs(t) do
        result = result .. '    ["' .. tostring(key) .. '"] = '
        if type(value) == "string" then
            result = result .. '"' .. value .. '"'
        elseif type(value) == "boolean" then
            result = result .. tostring(value)
        else
            result = result .. tostring(value)
        end
        result = result .. ",\n"
    end
    result = result .. "}"
    return result
end

-- 保存设置
function Settings.save()
    -- 将设置序列化为Lua表格式保存
    local settingsString = "return " .. tableToString(settings)
    local success, message = love.filesystem.write("settings.lua", settingsString)
    if not success then
        print("保存设置失败: " .. tostring(message))
    end
end

-- 加载设置
function Settings.load()
    -- 从Lua文件加载设置
    local info = love.filesystem.getInfo("settings.lua")
    if info then
        local contents, size = love.filesystem.read("settings.lua")
        if contents then
            local success, loadedSettings = pcall(loadstring or load, contents)
            if success and type(loadedSettings) == "function" then
                local success2, result = pcall(loadedSettings)
                if success2 and type(result) == "table" then
                    for key, value in pairs(result) do
                        settings[key] = value
                    end
                end
            end
        end
    end
end

-- 初始化设置
function Settings.init()
    Settings.load()

    -- 初始化本地化系统
    LocaleManager.init(settings.language)

    -- 应用显示模式（如果需要全屏）
    if settings.displayMode == "fullscreen" then
        love.window.setFullscreen(true, "desktop")
    end
    -- 窗口模式的尺寸已经在conf.lua中设置，无需重复设置
end

return Settings
