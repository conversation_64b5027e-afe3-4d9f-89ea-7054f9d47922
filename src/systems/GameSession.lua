-- 游戏会话管理器
-- 负责保存和恢复游戏进行中的状态数据
local GameSession = {}

-- 游戏会话数据
local sessionData = {
    -- 游戏对象
    player = nil,
    enemies = {},
    projectiles = {},
    pickups = {},
    explosions = {},
    damageNumbers = {},

    -- 游戏状态
    gameTime = 0,
    waveLevel = 1,
    enemySpawnTimer = 0,
    enemySpawnRate = 2.0,
    isPaused = false,

    -- 武器系统
    weaponCooldowns = {},

    -- 鼠标控制
    mouseTarget = {
        x = 0,
        y = 0
    },

    -- 地图设置
    mapWidth = 2000,
    mapHeight = 2000,

    -- 摄像机
    camera = {
        x = 0,
        y = 0,
        scale = 1.0
    }
}

-- 保存当前游戏状态
function GameSession.saveState()
    -- 深拷贝当前状态（简化版本，实际项目中可能需要更复杂的序列化）
    return {
        gameTime = sessionData.gameTime,
        waveLevel = sessionData.waveLevel,
        enemySpawnTimer = sessionData.enemySpawnTimer,
        enemySpawnRate = sessionData.enemySpawnRate,
        isPaused = sessionData.isPaused,
        weaponCooldowns = {unpack(sessionData.weaponCooldowns)},
        mouseTarget = {
            x = sessionData.mouseTarget.x,
            y = sessionData.mouseTarget.y
        },
        camera = {
            x = sessionData.camera.x,
            y = sessionData.camera.y,
            scale = sessionData.camera.scale
        },
        -- 游戏对象需要特殊处理
        playerData = sessionData.player and {
            x = sessionData.player.x,
            y = sessionData.player.y,
            invulnerableTime = sessionData.player.invulnerableTime,
            hitFlashTime = sessionData.player.hitFlashTime
        } or nil
        -- 可以根据需要添加更多数据
    }
end

-- 恢复游戏状态
function GameSession.restoreState(savedState)
    if not savedState then
        return
    end

    sessionData.gameTime = savedState.gameTime or 0
    sessionData.waveLevel = savedState.waveLevel or 1
    sessionData.enemySpawnTimer = savedState.enemySpawnTimer or 0
    sessionData.enemySpawnRate = savedState.enemySpawnRate or 2.0
    sessionData.isPaused = savedState.isPaused or false
    sessionData.weaponCooldowns = savedState.weaponCooldowns or {}

    if savedState.mouseTarget then
        sessionData.mouseTarget.x = savedState.mouseTarget.x
        sessionData.mouseTarget.y = savedState.mouseTarget.y
    end

    if savedState.camera then
        sessionData.camera.x = savedState.camera.x
        sessionData.camera.y = savedState.camera.y
        sessionData.camera.scale = savedState.camera.scale
    end

    -- 恢复玩家数据
    if savedState.playerData and sessionData.player then
        sessionData.player.x = savedState.playerData.x
        sessionData.player.y = savedState.playerData.y
        sessionData.player.invulnerableTime = savedState.playerData.invulnerableTime or 0
        sessionData.player.hitFlashTime = savedState.playerData.hitFlashTime or 0
    end
end

-- 初始化新的游戏会话
function GameSession.initialize()
    sessionData.player = nil
    sessionData.enemies = {}
    sessionData.projectiles = {}
    sessionData.pickups = {}
    sessionData.explosions = {}
    sessionData.damageNumbers = {}
    sessionData.gameTime = 0
    sessionData.waveLevel = 1
    sessionData.enemySpawnTimer = 0
    sessionData.enemySpawnRate = 2.0
    sessionData.isPaused = false
    sessionData.weaponCooldowns = {}
    sessionData.mouseTarget = {
        x = 0,
        y = 0
    }
    sessionData.camera = {
        x = 0,
        y = 0,
        scale = 1.0
    }
end

-- 清理游戏会话
function GameSession.cleanup()
    sessionData.player = nil
    sessionData.enemies = {}
    sessionData.projectiles = {}
    sessionData.pickups = {}
    sessionData.explosions = {}
    sessionData.damageNumbers = {}
end

-- 获取会话数据的引用（用于游戏逻辑）
function GameSession.getData()
    return sessionData
end

-- 设置玩家对象
function GameSession.setPlayer(player)
    sessionData.player = player
end

-- 获取玩家对象
function GameSession.getPlayer()
    return sessionData.player
end

-- 添加敌人
function GameSession.addEnemy(enemy)
    table.insert(sessionData.enemies, enemy)
end

-- 添加弹射体
function GameSession.addProjectile(projectile)
    table.insert(sessionData.projectiles, projectile)
end

-- 添加拾取物
function GameSession.addPickup(pickup)
    table.insert(sessionData.pickups, pickup)
end

-- 添加爆炸效果
function GameSession.addExplosion(explosion)
    table.insert(sessionData.explosions, explosion)
end

-- 添加伤害数字
function GameSession.addDamageNumber(damageNumber)
    table.insert(sessionData.damageNumbers, damageNumber)
end

-- 获取所有敌人
function GameSession.getEnemies()
    return sessionData.enemies
end

-- 获取所有弹射体
function GameSession.getProjectiles()
    return sessionData.projectiles
end

-- 获取所有拾取物
function GameSession.getPickups()
    return sessionData.pickups
end

-- 获取所有爆炸效果
function GameSession.getExplosions()
    return sessionData.explosions
end

-- 获取所有伤害数字
function GameSession.getDamageNumbers()
    return sessionData.damageNumbers
end

-- 设置暂停状态
function GameSession.setPaused(paused)
    sessionData.isPaused = paused
end

-- 获取暂停状态
function GameSession.isPaused()
    return sessionData.isPaused
end

-- 更新游戏时间
function GameSession.updateTime(dt)
    if not sessionData.isPaused then
        sessionData.gameTime = sessionData.gameTime + dt
    end
end

-- 获取游戏时间
function GameSession.getGameTime()
    return sessionData.gameTime
end

-- 设置波次等级
function GameSession.setWaveLevel(level)
    sessionData.waveLevel = level
end

-- 获取波次等级
function GameSession.getWaveLevel()
    return sessionData.waveLevel
end

-- 更新鼠标目标
function GameSession.updateMouseTarget(x, y)
    sessionData.mouseTarget.x = x
    sessionData.mouseTarget.y = y
end

-- 获取鼠标目标
function GameSession.getMouseTarget()
    return sessionData.mouseTarget
end

-- 获取摄像机
function GameSession.getCamera()
    return sessionData.camera
end

-- 获取地图尺寸
function GameSession.getMapSize()
    return sessionData.mapWidth, sessionData.mapHeight
end

return GameSession
