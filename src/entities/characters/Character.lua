-- 角色基类
-- 定义所有角色的通用属性和方法
local Character = {}
Character.__index = Character

-- 创建新角色
function Character:new(data)
    local instance = {
        id = data.id,
        name = data.name,
        description = data.description,
        baseHealth = data.baseHealth,
        baseDamage = data.baseDamage,
        baseSpeed = data.baseSpeed,
        startingWeapon = data.startingWeapon,
        unlocked = data.unlocked or false,

        -- 特殊属性
        specialAbilities = data.specialAbilities or {},
        passiveBonus = data.passiveBonus or {},

        -- 升级成长
        healthGrowth = data.healthGrowth or 1.0,
        damageGrowth = data.damageGrowth or 1.0,
        speedGrowth = data.speedGrowth or 1.0
    }

    setmetatable(instance, Character)
    return instance
end

-- 获取角色在指定等级的属性
function Character:getStatsAtLevel(level)
    level = level or 1
    return {
        health = math.floor(self.baseHealth * (1 + (level - 1) * self.healthGrowth * 0.1)),
        damage = math.floor(self.baseDamage * (1 + (level - 1) * self.damageGrowth * 0.1)),
        speed = math.floor(self.baseSpeed * (1 + (level - 1) * self.speedGrowth * 0.05))
    }
end

-- 获取显示名称（本地化）
function Character:getDisplayName()
    if _G.LocaleManager then
        return _G.LocaleManager.getText(self.name:lower())
    end
    return self.name
end

-- 获取显示描述（本地化）
function Character:getDisplayDescription()
    if _G.LocaleManager then
        return _G.LocaleManager.getText(self.name:lower() .. "_desc")
    end
    return self.description
end

-- 检查是否解锁
function Character:isUnlocked(gameData)
    if self.unlocked then
        return true
    end

    if gameData and gameData.persistent.unlockedCharacters then
        return gameData.persistent.unlockedCharacters[self.id] or false
    end

    return false
end

-- 获取解锁条件描述
function Character:getUnlockCondition()
    -- 可以在子类中重写
    return "完成特定条件解锁"
end

return Character
