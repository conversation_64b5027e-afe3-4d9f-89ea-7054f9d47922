-- 战士角色
-- 平衡的近战战士，拥有强大的生命力
local Character = require("src/entities/characters/Character")
local Warrior = {}

-- 战士数据
local warriorData = {
    id = 1,
    name = "Warrior",
    description = "平衡的近战战士，拥有强大的生命力",
    baseHealth = 120,
    baseDamage = 25,
    baseSpeed = 180,
    startingWeapon = "sword",
    unlocked = true,

    -- 战士特殊属性
    specialAbilities = {"berserker_rage", -- 狂暴
    "shield_bash" -- 盾击
    },

    passiveBonus = {
        healthMultiplier = 1.2, -- 20%额外生命
        defenseBonus = 5, -- 额外5点防御
        healingBonus = 0.1 -- 10%治疗加成
    },

    -- 成长系数（战士生命成长强）
    healthGrowth = 1.3,
    damageGrowth = 1.0,
    speedGrowth = 0.8
}

-- 创建战士实例
function Warrior.create()
    return Character:new(warriorData)
end

-- 战士特殊技能：狂暴
function Warrior.berserkerRage(player)
    -- 临时增加攻击力和攻击速度，但减少防御
    return {
        damageMultiplier = 1.5,
        attackSpeedMultiplier = 1.3,
        defenseMultiplier = 0.7,
        duration = 10
    }
end

-- 战士特殊技能：盾击
function Warrior.shieldBash(player)
    -- 造成伤害并眩晕敌人
    return {
        damage = player.damage * 1.2,
        stunDuration = 2.0,
        cooldown = 8.0
    }
end

-- 获取解锁条件
function Warrior.getUnlockCondition()
    return "默认解锁"
end

return Warrior
