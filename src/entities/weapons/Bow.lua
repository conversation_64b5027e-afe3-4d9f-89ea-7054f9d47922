-- 弓武器
-- 射出快速箭矢
local Weapon = require("src/entities/weapons/Weapon")
local Bow = {}

-- 弓武器数据
local bowData = {
    id = "bow",
    name = "Bow",
    description = "射出快速箭矢",
    damage = 20,
    cooldown = 0.6,
    range = 400,
    type = "projectile",
    maxLevel = 8,

    -- 升级加成（每级增加）
    upgradeBonuses = {
        damage = 5, -- 每级+5伤害
        range = 25, -- 每级+25射程
        cooldown = -0.05, -- 每级-0.05冷却
        pierce = 0.5 -- 每2级+1穿透
    },

    -- 特殊属性
    pierce = 0,
    area = 1.0,
    amount = 1,

    -- 特殊效果
    effects = {"piercing", -- 穿透效果
    "long_range" -- 远程
    },

    specialProperties = {
        projectileSpeed = 400,
        accuracy = 0.95,
        criticalChanceBonus = 0.1, -- 额外10%暴击率
        projectileType = "arrow"
    },

    -- 进化要求
    evolutionRequirements = {
        level = 8,
        requiredWeapons = {"quiver"} -- 需要箭袋进化
    },
    evolutionTarget = "multi_shot_bow"
}

-- 创建弓实例
function Bow.create()
    return Weapon:new(bowData)
end

-- 弓的升级描述
function Bow.getUpgradeDescriptions()
    return {
        [2] = "增加伤害和射程",
        [3] = "提高射击速度",
        [4] = "箭矢可穿透1个敌人",
        [5] = "大幅增加射程",
        [6] = "穿透2个敌人",
        [7] = "提高暴击率",
        [8] = "可进化为多重射击弓"
    }
end

return Bow
