-- 魔法弹武器
-- 追踪敌人的魔法弹
local Weapon = require("src/entities/weapons/Weapon")
local MagicMissile = {}

-- 魔法弹武器数据
local magicMissileData = {
    id = "magic_missile",
    name = "Magic Missile",
    description = "追踪敌人的魔法弹",
    damage = 25,
    cooldown = 0.8,
    range = 300,
    type = "magic",
    maxLevel = 8,

    -- 升级加成（每级增加）
    upgradeBonuses = {
        damage = 6, -- 每级+6伤害
        range = 20, -- 每级+20射程
        cooldown = -0.08, -- 每级-0.08冷却
        amount = 0.3 -- 每3级+1个弹丸
    },

    -- 特殊属性
    pierce = 0,
    area = 1.0,
    amount = 1,

    -- 特殊效果
    effects = {"homing", -- 追踪效果
    "magic_damage" -- 魔法伤害
    },

    specialProperties = {
        projectileSpeed = 250,
        homingStrength = 0.8,
        maxTurnRate = 180, -- 度/秒
        magicType = "arcane"
    },

    -- 进化要求
    evolutionRequirements = {
        level = 8,
        requiredWeapons = {"spell_focus"} -- 需要法术焦点进化
    },
    evolutionTarget = "arcane_orb"
}

-- 创建魔法弹实例
function MagicMissile.create()
    return Weapon:new(magicMissileData)
end

-- 魔法弹的特殊效果：追踪
function MagicMissile.homingEffect(projectile, target, dt)
    if not target or not projectile then
        return
    end

    -- 计算朝向目标的方向
    local dx = target.x - projectile.x
    local dy = target.y - projectile.y
    local targetAngle = math.atan2(dy, dx)

    -- 计算角度差
    local angleDiff = targetAngle - projectile.angle

    -- 标准化角度差到 [-π, π] 范围
    while angleDiff > math.pi do
        angleDiff = angleDiff - 2 * math.pi
    end
    while angleDiff < -math.pi do
        angleDiff = angleDiff + 2 * math.pi
    end

    -- 应用转向
    local maxTurnRate = math.rad(magicMissileData.specialProperties.maxTurnRate)
    local turnAmount = math.min(math.abs(angleDiff), maxTurnRate * dt)

    if angleDiff > 0 then
        projectile.angle = projectile.angle + turnAmount
    else
        projectile.angle = projectile.angle - turnAmount
    end

    -- 更新速度
    local speed = magicMissileData.specialProperties.projectileSpeed
    projectile.vx = math.cos(projectile.angle) * speed
    projectile.vy = math.sin(projectile.angle) * speed
end

-- 魔法弹的升级描述
function MagicMissile.getUpgradeDescriptions()
    return {
        [2] = "增加伤害和射程",
        [3] = "提高发射速度",
        [4] = "增强追踪能力",
        [5] = "发射2个魔法弹",
        [6] = "穿透1个敌人",
        [7] = "发射3个魔法弹",
        [8] = "可进化为奥术法球"
    }
end

-- 魔法弹的目标选择逻辑
function MagicMissile.selectTarget(playerX, playerY, enemies, range)
    local closestEnemy = nil
    local closestDistance = range

    for _, enemy in ipairs(enemies) do
        local distance = math.sqrt((enemy.x - playerX) ^ 2 + (enemy.y - playerY) ^ 2)

        if distance < closestDistance then
            closestDistance = distance
            closestEnemy = enemy
        end
    end

    return closestEnemy
end

return MagicMissile
