-- 武器基类
-- 定义所有武器的通用属性和方法
local Weapon = {}
Weapon.__index = Weapon

-- 创建新武器
function Weapon:new(data)
    local instance = {
        id = data.id,
        name = data.name,
        description = data.description,
        damage = data.damage,
        cooldown = data.cooldown,
        range = data.range,
        type = data.type, -- "melee", "projectile", "magic"

        -- 升级属性
        level = 1,
        maxLevel = data.maxLevel or 8,
        upgradeBonuses = data.upgradeBonuses or {},

        -- 特殊属性
        pierce = data.pierce or 0,
        area = data.area or 1.0,
        duration = data.duration or 1.0,
        amount = data.amount or 1,

        -- 效果和特性
        effects = data.effects or {},
        specialProperties = data.specialProperties or {},

        -- 进化路径
        evolutionRequirements = data.evolutionRequirements or {},
        evolutionTarget = data.evolutionTarget or nil
    }

    setmetatable(instance, Weapon)
    return instance
end

-- 获取武器在指定等级的属性
function Weapon:getStatsAtLevel(level)
    level = math.min(level or self.level, self.maxLevel)

    local stats = {
        damage = self.damage,
        cooldown = self.cooldown,
        range = self.range,
        pierce = self.pierce,
        area = self.area,
        duration = self.duration,
        amount = self.amount
    }

    -- 应用升级加成
    if self.upgradeBonuses then
        for statName, bonus in pairs(self.upgradeBonuses) do
            if stats[statName] then
                stats[statName] = stats[statName] + (bonus * (level - 1))
            end
        end
    end

    return stats
end

-- 升级武器
function Weapon:upgrade()
    if self.level < self.maxLevel then
        self.level = self.level + 1
        return true
    end
    return false
end

-- 检查是否可以进化
function Weapon:canEvolve(playerWeapons)
    if not self.evolutionRequirements or not self.evolutionTarget then
        return false
    end

    -- 检查等级要求
    if self.level < (self.evolutionRequirements.level or self.maxLevel) then
        return false
    end

    -- 检查需要的其他武器
    if self.evolutionRequirements.requiredWeapons then
        for _, requiredWeaponId in ipairs(self.evolutionRequirements.requiredWeapons) do
            local hasWeapon = false
            for _, weapon in ipairs(playerWeapons) do
                if weapon.id == requiredWeaponId then
                    hasWeapon = true
                    break
                end
            end
            if not hasWeapon then
                return false
            end
        end
    end

    return true
end

-- 获取显示名称（本地化）
function Weapon:getDisplayName()
    if _G.LocaleManager then
        return _G.LocaleManager.getText(self.id)
    end
    return self.name
end

-- 获取显示描述（本地化）
function Weapon:getDisplayDescription()
    if _G.LocaleManager then
        return _G.LocaleManager.getText(self.id .. "_desc")
    end
    return self.description
end

-- 获取升级描述
function Weapon:getUpgradeDescription(targetLevel)
    targetLevel = targetLevel or self.level + 1
    if targetLevel > self.maxLevel then
        return "已达到最大等级"
    end

    local currentStats = self:getStatsAtLevel(self.level)
    local nextStats = self:getStatsAtLevel(targetLevel)

    local changes = {}
    for statName, nextValue in pairs(nextStats) do
        local currentValue = currentStats[statName]
        if nextValue ~= currentValue then
            local change = nextValue - currentValue
            local changeStr = change > 0 and ("+" .. change) or tostring(change)
            table.insert(changes, statName .. ": " .. changeStr)
        end
    end

    return table.concat(changes, ", ")
end

return Weapon
