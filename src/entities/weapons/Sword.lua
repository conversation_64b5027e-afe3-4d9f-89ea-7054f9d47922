-- 剑武器
-- 基础近战武器
local Weapon = require("src/entities/weapons/Weapon")
local Sword = {}

-- 剑武器数据
local swordData = {
    id = "sword",
    name = "Sword",
    description = "基础近战武器",
    damage = 30,
    cooldown = 1.0,
    range = 60,
    type = "melee",
    maxLevel = 8,

    -- 升级加成（每级增加）
    upgradeBonuses = {
        damage = 8, -- 每级+8伤害
        range = 5, -- 每级+5射程
        cooldown = -0.1 -- 每级-0.1冷却
    },

    -- 特殊属性
    pierce = 0,
    area = 1.2,

    -- 特殊效果
    effects = {"cleave" -- 劈砍效果
    },

    specialProperties = {
        meleeType = "slash",
        canBlock = true,
        weaponSpeed = "medium"
    },

    -- 进化要求
    evolutionRequirements = {
        level = 8,
        requiredWeapons = {"shield"} -- 需要盾牌进化为剑盾组合
    },
    evolutionTarget = "sword_and_shield"
}

-- 创建剑实例
function Sword.create()
    return Weapon:new(swordData)
end

-- 剑的特殊效果：劈砍
function Sword.cleaveEffect(weapon, targetPosition, enemies)
    local cleaveTargets = {}
    local cleaveRange = weapon.range * 1.5

    for _, enemy in ipairs(enemies) do
        local distance = math.sqrt((enemy.x - targetPosition.x) ^ 2 + (enemy.y - targetPosition.y) ^ 2)

        if distance <= cleaveRange then
            table.insert(cleaveTargets, {
                enemy = enemy,
                damage = weapon.damage * 0.7 -- 劈砍伤害为70%
            })
        end
    end

    return cleaveTargets
end

-- 剑的升级描述
function Sword.getUpgradeDescriptions()
    return {
        [2] = "增加伤害和射程",
        [3] = "提高攻击速度",
        [4] = "增强劈砍效果",
        [5] = "伤害大幅提升",
        [6] = "获得格挡能力",
        [7] = "劈砍范围扩大",
        [8] = "可进化为剑盾组合"
    }
end

return Sword
