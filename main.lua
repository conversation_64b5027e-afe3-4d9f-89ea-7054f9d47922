-- 幸存者传说 - 主程序文件
-- 使用模块化架构
-- 核心模块
local StateManager = require("src/core/StateManager")
local Settings = require("src/config/Settings")
local GameData = require("src/core/GameData")
local CursorManager = require("src/core/CursorManager")

-- 导入UI模块
local FontManager = require("src/ui/FontManager")
local UIManager = require("src/ui/UIManager")
local LocaleManager = require("src/locales/LocaleManager")

-- 导入实体管理器
local CharacterManager = require("src/entities/characters/CharacterManager")
local WeaponManager = require("src/entities/weapons/WeaponManager")

-- 导入所有状态
local MenuState = require("src/states/MenuState")
local GameState = require("src/states/GameState")
local PauseState = require("src/states/PauseState")
local SettingsState = require("src/states/SettingsState")
local CharacterSelectState = require("src/states/CharacterSelectState")
local UpgradeState = require("src/states/UpgradeState")
local GameOverState = require("src/states/GameOverState")

-- 导出模块到全局（便于状态文件访问）
_G.StateManager = StateManager
_G.GameData = GameData
_G.CursorManager = CursorManager
_G.Settings = Settings
_G.FontManager = FontManager
_G.UIManager = UIManager
_G.LocaleManager = LocaleManager
_G.CharacterManager = CharacterManager
_G.WeaponManager = WeaponManager

function love.load()
    -- 初始化设置
    Settings.init()

    -- 最大化窗口（自适应所有屏幕尺寸）
    love.window.maximize()

    -- 预加载字体
    FontManager.preloadFonts()

    -- 初始化光标系统
    CursorManager.init()

    -- 初始化UI管理器
    UIManager.init()

    -- 注册所有状态
    StateManager.registerState("menu", MenuState)
    StateManager.registerState("game", GameState)
    StateManager.registerState("pause", PauseState)
    StateManager.registerState("settings", SettingsState)
    StateManager.registerState("character_select", CharacterSelectState)
    StateManager.registerState("upgrade", UpgradeState)
    StateManager.registerState("gameover", GameOverState)

    -- 启动主菜单
    StateManager.changeState("menu")
end

function love.update(dt)
    StateManager.update(dt)
    UIManager.update(dt)
end

function love.draw()
    StateManager.draw()
    UIManager.draw()
end

function love.keypressed(key)
    -- 全局快捷键
    if key == "f11" then
        Settings.toggleDisplayMode()
        Settings.save()
    elseif key == "f4" and love.keyboard.isDown("lalt") then
        love.event.quit()
    else
        StateManager.keypressed(key)
    end
end

function love.keyreleased(key)
    StateManager.keyreleased(key)
end

function love.mousepressed(x, y, button)
    -- 先让UI管理器处理，如果没有处理再传给状态管理器
    if not UIManager.mousepressed(x, y, button) then
        StateManager.mousepressed(x, y, button)
    end
end

function love.mousereleased(x, y, button)
    UIManager.mousereleased(x, y, button)
    StateManager.mousereleased(x, y, button)
end

function love.mousemoved(x, y, dx, dy)
    UIManager.mousemoved(x, y, dx, dy)
    StateManager.mousemoved(x, y, dx, dy)
end

function love.resize(w, h)
    -- 处理窗口大小变化
    -- 可以在这里处理UI重新布局等逻辑
end

function love.quit()
    -- 保存设置
    Settings.save()
end
